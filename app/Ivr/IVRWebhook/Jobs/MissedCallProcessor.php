<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Models\IvrEventPush;
use App\Ivr\Services\IvrCallRecordService;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class MissedCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
        private readonly AutoAssignAgent $autoAssignAgent,
        private readonly AssignAgent $assignAgent,
        private readonly IvrCallRecordService $callRecordService
    ) {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::Missed;
    }

    public function process(IvrEventPush $ivrEventPush): void
    {
        Log::info('Missed call event detected', [
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);

        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse($ivrEventPush->payload, $this->state());

        // Find or create call record
        $call = $this->callRecordService->findOrCreateCallRecord(
            callUuid: $ivrEventPush->external_id,
            eventPayload: $eventPayload,
            vendorId: $ivrEventPush->vendor_id,
            eventType: 'Missed call'
        );

        // Update call record with missed call details
        $this->callRecordService->updateCallRecord(
            call: $call,
            eventPayload: $eventPayload,
            eventType: 'Missed call'
        );

        $this->assignAgent->forCall(call: $call, eventPayload: $eventPayload);

        // Auto Assign agent if needed
        if (! $eventPayload->hasAgentNumber()) {
            Log::info('Call has no agent assigned, preparing to auto assign agent', [
                'enquiry_id' => $call->fk_int_enquiry_id,
            ]);

            $this->autoAssignAgent->for(enquiryId: $call->fk_int_enquiry_id, vendorId: $call->vendor_id);
        }

        // Send notification
        Bus::dispatch(new SendNotificationOnEnquiry(
            new NotificationPayload(
                vendorId: $ivrEventPush->vendor_id,
                enquiryId: $call->fk_int_enquiry_id,
                calledNumber: $eventPayload->calledNumber,
                callerNumber: $eventPayload->customerPhoneNumber,
                callType: 'missed_call'
            )
        ));

        Log::info('Missed call processing completed', [
            'enquiry_id' => $call->fk_int_enquiry_id,
            'call_uuid' => $ivrEventPush->external_id,
        ]);
    }
}
