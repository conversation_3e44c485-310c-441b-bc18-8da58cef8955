<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\Models\Ivr;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;

class AssignAgent
{
    public function forCall(Ivr $call, EventPayload $eventPayload): void
    {
        if ($call->agent_number) {
            Log::info('Agent already assigned to call, skipping agent assignment', [
                'agent_number' => $call->agent_number,
            ]);
            return;
        }

        if (! $eventPayload->hasAgentNumber()) {
            Log::info('Webhook has no agent assigned, So skipping agent assignment', [
                'enquiry_id' => $call->fk_int_enquiry_id,
            ]);
            return;
        }

        Log::withContext([
            'enquiry_id' => $call->fk_int_enquiry_id,
            'call_uuid' => $call->call_uuid,
            'agent_number' => $eventPayload->agentPhoneNumber,
        ])->info('Preparing to assign agent to call enquiry');

        $call->update([
            'agent_number' => $eventPayload->agentPhoneNumber instanceof PhoneNumber
                ? $eventPayload->agentPhoneNumber->toPhoneNumber()
                : $eventPayload->agentPhoneNumber,
        ]);

        if (! $eventPayload->agentPhoneNumber instanceof PhoneNumber) {
            Log::info('Agent number is not a phone number, skipping agent assignment');
            return;
        }

        $agent = User::query()
            ->where('mobile', $eventPayload->agentPhoneNumber->nationalNumber())
            ->where('parent_user_id', $call->vendor_id)
            ->first();

        if (! $agent instanceof User) {
            Log::info('Unable to assign agent to call enquiry as agent not found', [
                'agent_number' => (string) $eventPayload->agentPhoneNumber,
            ]);
            return;
        }

        $enquiry = Enquiry::query()
            ->whereKey($call->fk_int_enquiry_id)
            ->first(['pk_int_enquiry_id', 'staff_id']);

        if ($enquiry->staff_id) {
            return;
        }

        $enquiry->update([
            'staff_id' => $agent->pk_int_user_id,
            'assigned_date' => Carbon::today(),
        ]);

        Event::dispatch(new CreateFollowup(
            note: $agent->vchr_user_name . ' has been designated as the lead via IVR',
            log_type: EnquiryFollowup::TYPE_ACTIVITY,
            enquiry_id: $enquiry->pk_int_enquiry_id,
            created_by: $call->vendor_id
        ));

        Log::info('Agent assigned to enquiry', [
            'agent_number' => (string) $eventPayload->agentPhoneNumber,
            'agent_name' => $agent->vchr_user_name,
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
        ]);

    }
}
