<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Common\SendTelegram;
use App\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Shared\ValueObjects\PhoneNumber;

final class NotifyWhenBonvoiceClickToCallFailed implements ShouldQueue
{
    public string $queue = 'telegram';

    public function __construct(
        public readonly int         $userId,
        public readonly int         $vendorId,
        public readonly PhoneNumber $customerPhone,
        public readonly array       $body,
        public readonly array       $response = []
    )
    {
    }

    public function handle(SendTelegram $sender): void
    {
        // Get user and vendor names
        $userName = User::getUserName($this->userId);
        $vendorName = User::getUserName($this->vendorId);

        // Build the notification message
        $message = $this->buildNotificationMessage($userName, $vendorName);

        $sender->telegram('-4793585985', $message);
    }

    private function buildNotificationMessage(string $userName, string $vendorName): string
    {
        $message = "🚨 <b>Bonvoice Click-to-Call Failed</b>\n\n";

        $message .= "📋 <b>Details:</b>\n";
        $message .= "👤 <b>User:</b> {$userName}\n";
        $message .= "🏢 <b>Vendor:</b> {$vendorName}\n";
        $message .= "📞 <b>Customer Phone:</b> {$this->customerPhone->toE164PhoneNumber()}\n\n";

        // Add request body information if available
        if (!empty($this->body)) {
            $message .= "📤 <b>Request Payload:</b>\n";
            $message .= "<pre>" . json_encode($this->body, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "</pre>\n\n";
        }

        // Add response information if available
        if (!empty($this->response)) {
            $message .= "📥 <b>Response Received:</b>\n";
            $message .= "<pre>" . json_encode($this->response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "</pre>\n\n";
        }

        $message .= "⚠️ <i>Please investigate this failed click-to-call request and take appropriate action.</i>";

        return $message;
    }
}
