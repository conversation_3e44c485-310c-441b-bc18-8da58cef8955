<?php

declare(strict_types=1);

namespace App\ActivityLogger;

use App\ActivityLogger\Events\ModelAttributeChanged;
use App\ActivityLogger\Listeners\LogModelActivity;
use App\ActivityLogger\Observers\EnquiryObserver;
use App\ActivityLogger\Services\ActivityLoggerService;
use App\BackendModel\Enquiry;
use Illuminate\Foundation\Support\Providers\EventServiceProvider;

/**
 * Service Provider for Activity Logger Module.
 */
final class ActivityLoggerServiceProvider extends EventServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        ModelAttributeChanged::class => [
            LogModelActivity::class,
        ],
    ];

    /**
     * Register any application services.
     */
    public function register(): void
    {
        parent::register();

        // Register the ActivityLoggerService as a singleton
        $this->app->singleton(ActivityLoggerService::class, function () {
            return new ActivityLoggerService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        parent::boot();

        $this->registerObservers();
        $this->registerRoutes();
    }

    /**
     * Register model observers.
     */
    private function registerObservers(): void
    {
      //  Enquiry::observe(EnquiryObserver::class);
    }

    /**
     * Register routes.
     */
    private function registerRoutes(): void
    {
    }
}
