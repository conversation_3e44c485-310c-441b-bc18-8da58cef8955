<?php

declare(strict_types=1);

namespace App\BackendModel;

use App\Common\Notifications;
use App\Common\SingleSMS;
use App\CustomFieldValue;
use App\Ivr\Models\Ivr;
use App\Task;
use App\User;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string|null $name customer name
 * @property string|null $note customer email
 * @property string|null $log_type
 * @property string|null $task_type email subject
 * @property int|null $task_id
 * @property int|null $assigned_to
 * @property string|null $date
 * @property int|null $reminder
 * @property string|null $response cc mail
 * @property string|null $duration content
 * @property int $enquiry_id
 * @property string|null $bcc
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $old_status_id
 * @property int|null $checkout_id
 * @property-read User|null $added_by
 * @property-read User|null $assigned
 * @property-read \App\CallMaster|null $callMaster
 * @property-read EnquiryFollowup|null $checkOut
 * @property-read \App\BackendModel\Checkout_note|null $checkin
 * @property-read \App\BackendModel\FeedbackStatus|null $currentStatus
 * @property-read \Illuminate\Database\Eloquent\Collection<int, CustomFieldValue> $custom_field_values
 * @property-read int|null $custom_field_values_count
 * @property-read \App\BackendModel\Enquiry|null $enquiry
 * @property-read mixed $ivr_voice_url
 * @property-read Ivr|null $ivrdata
 * @property-read \App\BackendModel\EnquiryLocation|null $location
 * @property-read \App\BackendModel\FeedbackStatus|null $oldStatus
 * @property-read \App\BackendModel\EnquiryPurpose|null $purpose
 * @property-read \App\BackendModel\EnquiryType|null $source
 * @property-read Task|null $task
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup fromDate($start_date, $end_date)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereAssignedTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereBcc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereCheckoutId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereEnquiryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereLogType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereOldStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereReminder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereTaskType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnquiryFollowup whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class EnquiryFollowup extends Model
{
    public const TYPE_NEW = 0;

    public const TYPE_NOTE = 1;

    public const TYPE_LOG_CALL = 2;

    public const TYPE_LOG_EMAIL = 3;

    public const TYPE_LOG_MEETING = 4;

    public const TYPE_TASK = 5;

    public const TYPE_SCHEDULE = 6;

    public const TYPE_STATUS = 7;

    public const TYPE_EMAIL = 8;

    public const WHATSAPP_HISTORY = 9;

    public const SMS_HISTORY = 10;

    public const IVR = 11;

    public const ENQ_PURPOSE = 12;

    public const TYPE_VOICE_NOTE = 13;

    public const TYPE_FILE_NOTE = 14;

    public const TYPE_ORDER = 15;

    public const TYPE_INDIA_MART = 16;

    public const TYPE_ACTIVITY = 17;

    /**
     * added by goutham
     */
    public const TYPE_DOCUMENT = 18;

    public const TYPE_CHECKIN = 19;

    public const TYPE_CHECKOUT = 20;

    public const TYPE_NEW_LEAD = 21;

    public const TYPE_TASK_NEW = 22;

    public const TYPE_TASK_HISTORY = 23;

    public const TYPE_DEAL = 25;

    public const TYPE_VISIT = 27;

    public const TYPE_DELETE = 28;

    public const TASK_COMPLETED = 29;

    public const TYPE_AGENCY = 30;

    protected $table = 'tbl_enquiry_followups';

    protected $fillable = ['assinged_to', 'created_by', 'enquiry_id', 'note', 'name', 'log_type'];

    protected $guarded = [];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'assigned_to');
    }

    public function added_by()
    {
        return $this->belongsTo('App\User', 'created_by');
    }

    public function assigned()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function currentStatus()
    {
        return $this->belongsTo(FeedbackStatus::class, 'note');
    }

    public function oldStatus()
    {
        return $this->belongsTo(FeedbackStatus::class, 'old_status_id');
    }

    public function source()
    {
        return $this->belongsTo(EnquiryType::class, 'note');
    }

    public function purpose()
    {
        return $this->belongsTo(EnquiryPurpose::class, 'note');
    }

    public function ivrdata()
    {
        return $this->hasOne(Ivr::class, 'call_uuid', 'note');
    }

    public function callMaster()
    {
        return $this->hasOne('App\CallMaster', 'id', 'task_id');
    }

    public function custom_field_values()
    {
        return $this->hasMany(CustomFieldValue::class, 'related_id', 'id');
    }

    public function enquiry()
    {
        return $this->belongsTo('App\BackendModel\Enquiry', 'enquiry_id');
    }

    public function task()
    {
        return $this->belongsTo(Task::class, 'task_id');
    }

    public function location()
    {
        return $this->belongsTo(EnquiryLocation::class, 'bcc');
    }

    public static function addSchedule($input, $userid): void
    {
        //Create Task
        $enquiry = Enquiry::query()->where('pk_int_enquiry_id', $input['id'])->first();
        $task_data = [
            'name' => $input['name'],
            'description' => $input['note'],
            'task_category_id' => 2,
            'assigned_to' => $enquiry->staff_id ?? $userid,
            'assigned_by' => $userid,
            'vendor_id' => $enquiry->fk_int_user_id,
            'enquiry_id' => $input['id'],
            'comment' => '',
            'status' => 0,
        ];
        if ($input['date'] != ' ' && $input['time'] != '') {
            $task_data['scheduled_date'] = $input['date'];
        } else {
            $task_data['scheduled_date'] = now();
        }

        $task = Task::create($task_data);

        //Get VendorId
        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $enquiry->followup_required = isset($input['followup_required']) ? $input['followup_required'] : '';
        $enquiry->lead_attension = 0;
        $enquiry->updated_at = now();
        $enquiry->save();
    }

    /**
     * @static
     * Function to update the created schedule
     *
     * @param array $input
     * @param int $id
     * @param int $userid
     */
    public static function updateSchedule($input, $id, $userid): void
    {
        $enquiryfollowups = self::find($id);
        $oldDate = $enquiryfollowups->date;
        $enquiryfollowups->name = $input['name'];
        $enquiryfollowups->note = $input['note'];
        $enquiryfollowups->duration = $input['duration'];
        if ($input['date'] != ' ' && $input['time'] != '') {
            $date_time = Carbon::createFromFormat(
                    'm/d/Y',
                    $input['date']
                )->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
            $enquiryfollowups->date = $date_time;
        }
        $enquiryfollowups->updated_by = $userid;
        $enquiryfollowups->save();
        //Update Task
        $enquiry = Enquiry::where('pk_int_enquiry_id', $enquiryfollowups->enquiry_id)->first();
        $task_id = $enquiryfollowups->task_id;
        $task_data = [
            'name' => $input['name'],
            'description' => $input['note'],
            'task_category_id' => 2,
            'assigned_to' => $enquiry->staff_id ?? Auth::user()->pk_int_user_id,
            'assigned_by' => Auth::user()->pk_int_user_id,
            'vendor_id' => $enquiry->fk_int_user_id,
        ];
        if ($input['date'] != ' ' && $input['time'] != '') {
            $task_data['scheduled_date'] = $enquiryfollowups->date;
        }
        if ($task_id) {
            Task::where('id', $task_id)->update($task_data);
        } else {
            $task_data['enquiry_id'] = $enquiryfollowups->enquiry_id;
            $task_data['status'] = 0;
            $task = Task::create($task_data);
            $enquiryfollowups->task_id = $task->id;
            $enquiryfollowups->save();
        }
        //
        //Get VendorId
        $enquiry = Enquiry::where('pk_int_enquiry_id', $enquiryfollowups->enquiry_id)->first();
        $vendorId = $enquiry->fk_int_user_id;

        //Notifications
        if ($oldDate == $date_time) {
            return;
        }

        $userObject = User::getUserDetails($vendorId);
        $userAdminObject = User::getSingleAdminDetails();
        $notifications = new Notifications();
        $from = env('MAIL_FROM_ADDRESS');
        $to = $userObject->email;
        $subject = 'Schedule';
        $name = $userObject->vchr_user_name;
        $logo = $userObject->vchr_logo;
        $attachment = '';
        $telegramId = $userObject->telegram_id;
        $mobileNumber = $userObject->vchr_user_mobile;
        $defaultSenderIdAdmin = SingleSMS::getSenderid($userAdminObject->pk_int_user_id, '');
        $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');

        $content1 = 'Meeting (' . $input['name'] . ') scheduled on ' . Carbon::parse($oldDate)->format(
                'M d'
            ) . ' at ' . Carbon::parse($oldDate)->format('g:i a') . ' updated to ' . Carbon::parse(
                $date_time
            )->format('M d') . ' at ' . Carbon::parse($date_time)->format('g:i a');
        $content2 = 'You have a meeting (' . $input['name'] . ') scheduled  on ' . Carbon::parse($oldDate)->format(
                'M d'
            ) . ' at ' . Carbon::parse($oldDate)->format('g:i a') . ' updated to ' . Carbon::parse(
                $date_time
            )->format('M d') . ' at ' . Carbon::parse($date_time)->format('g:i a');
        $notifications->notifications(
            $from,
            $to,
            $subject,
            $name,
            $content1,
            $content2,
            $logo,
            $attachment,
            $telegramId,
            $vendorId,
            $mobileNumber,
            $defaultRouteAdmin,
            $defaultSenderIdAdmin
        );
    }

    public function getIvrVoiceUrlAttribute()
    {
        if ($this->ivrdata && $this->ivrdata->recording_url) {
            if (filter_var($this->ivrdata->recording_url, FILTER_VALIDATE_URL)) {
                $url = $this->ivrdata->recording_url;
            } else {
                $url = 'https://pbx.voxbaysolutions.com/callrecordings/' . str_replace(
                        ' ',
                        '+',
                        $this->ivrdata->recording_url
                    );
            }
        } elseif ($this->callMaster && $this->callMaster->recording_url) {
            $url = $this->callMaster->recording_url;
        } else {
            $url = '';
        }

        return $url;
    }

    public static function tasks($enqId, $month)
    {
        $date = date_parse($month);
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.call_status_id', 'tasks.description',
            'tasks.comment', 'tasks.status',
            'tasks.created_at',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
        ];
        $tasksQ = Task::query()
            ->where('tasks.vendor_id', User::getVendorId())
            ->where('enquiry_id', $enqId)
            ->whereMonth('tasks.created_at', $date['month'])
            ->whereYear('tasks.created_at', $date['year'])
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->orderBy('tasks.id', 'DESC')
            ->select($selectColumns);

        return $tasksQ->get();
    }

    public function checkin()
    {
        return $this->hasOne('App\BackendModel\Checkout_note', 'id', 'note');
    }

    public function checkOut()
    {
        return $this->hasOne('App\BackendModel\EnquiryFollowup', 'id', 'checkout_id')->select(
            'id',
            'checkout_id',
            'note',
            DB::raw("DATE_FORMAT(created_at, '%d %M %Y %H:%i') as date")
        );
    }

    public function scopeFromDate($query, $start_date, $end_date)
    {
        return $query->whereDate('created_at', '>=', $start_date)
            ->whereDate('created_at', '<=', $end_date)->get();
    }
}
