<?php

namespace App\Http\Controllers\Guest;

use App\AgentStaff;
use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\GroupUsers;
use App\BackendModel\VirtualNumber;
use App\CallMaster;
use App\Common\Application;
use App\Common\Notifications;
use App\Common\Variables;
use App\FrontendModel\AssignAgent;
use App\Http\Controllers\Controller;
use App\Ivr\IVRWebhook\Jobs\NotifyWhenBonvoiceClickToCallFailed;
use App\Jobs\SendNotification;
use App\Task;
use App\Traits\CloudCallTrait;
use App\User;
use Carbon\Carbon;
use App\Events\CreateFollowup;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Shared\ValueObjects\PhoneNumber;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Http\Request;


class BonvoiceController extends Controller
{
    use CloudCallTrait;

    const VOXBAY = 1;
    const BONVOICE = 2;
    protected $user;
    protected $userId;

    public function bonvoiceCallResponse()
    {
        Log::info('Receiving IVR Calls from Bonvoice', [
            'request' => request()->all()
        ]);

        try {
            $input = request()->all();

            $validator = Validator::make($input, [
                'DisplayNumber' => 'required'
            ]);
            if ($validator->passes()) {
                if (request()->has('SourceNumber') && strlen(request()->SourceNumber) == 11 && substr(request()->SourceNumber, 0, 1) == "0")
                    request()->merge([
                        'SourceNumber' => '91' . substr(request()->SourceNumber, 1, 10)
                    ]);
                if (request()->has('SourceNumber') && strlen(request()->SourceNumber) == 10)
                    request()->merge([
                        'SourceNumber' => '91' . request()->SourceNumber
                    ]);

                if (request()->has('DisplayNumber') && strlen(request()->DisplayNumber) == 11 && substr(request()->DisplayNumber, 0, 1) == "0")
                    request()->merge([
                        'DID' => '91' . substr(request()->DisplayNumber, 1, 10)
                    ]);
                if (request()->has('SourceNumber') && strlen(request()->DisplayNumber) == 10)
                    request()->merge([
                        'DID' => '91' . request()->DisplayNumber
                    ]);

                //Get Userid from Virtual No
                $getVirtualNumber = VirtualNumber::where('vchr_virtual_number', request()->DID)->where('int_status', 1)->where('type', EnquiryType::IVR)->first();
                $existDisplayNumber = CallMaster::where('call_uuid', request()->callID)->first();
                // \Log::info('did'.request()->DID); \Log::info('getVirtualNumber- '.$getVirtualNumber);
                // \Log::info('callID'.request()->callID); \Log::info('existDisplayNumber- '.$existDisplayNumber);
                if ($getVirtualNumber) {
                    $vendorId = $getVirtualNumber->fk_int_user_id;
                    $vendor_id = User::getVendorIdApi($vendorId);
                    // \Log::info('vendor_id'.$vendor_id);
                    //Notifications
                    $log_type_label = $this->notificationInitialisation($vendorId);
                    $blockSubscribedUsers = $this->toCheckSubscribeUser();

                    //    if($vendor_id==5035){ \Log::info('blockSubscribedUsers- '.$blockSubscribedUsers);}
                    if ($blockSubscribedUsers) {
                        if (request()->has('SourceNumber') && request()->has('DisplayNumber')) {
                            $enq = Enquiry::where('fk_int_user_id', $vendor_id)
                                ->where('vchr_customer_mobile', request()->SourceNumber)
                                ->orderBy('vchr_customer_name', 'ASC')
                                ->first();
                            if (!request()->has('status') && !request()->filled('status')) {
                                request()->merge([
                                    'status' => 'New'
                                ]);
                            }
                            $insertCrm = Enquiry::getCRMUsers(EnquiryType::IVR, request()->SourceNumber, $vendorId, request()->all());
                            $enqnE = Enquiry::where('fk_int_user_id', $vendorId)->where('pk_int_enquiry_id', $insertCrm)->first();
                            if (request()->has('callType') && request('callType') == '1' && request()->has('DestinationNumber')) {
                                $mob = request('DestinationNumber');
                                $User = User::select('pk_int_user_id')->where('mobile', substr($mob, -10))->first();
                                if ($User) {
                                    if ($enqnE) {
                                        if ($enqnE->staff_id == null) {
                                            $enqnE->staff_id = $User->pk_int_user_id;
                                            $enqnE->assigned_date = Carbon::today();
                                            $enqnE->save();
                                            $agent_name = $User ? $User->vchr_user_name : 'Agent Not Exist';
                                            $note = $agent_name . " has been designated as the lead via IVR";
                                            event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enqnE->pk_int_enquiry_id, $vendorId));
                                        }
                                    }
                                }
                            } elseif (request('callType') == 0) {
                                if ($getVirtualNumber->agent_id) {
                                    if ($enqnE->staff_id == null) {
                                        $enqnE->staff_id = $getVirtualNumber->agent_id;
                                        $enqnE->assigned_date = Carbon::today();
                                        $enqnE->save();
                                        $User = User::find($getVirtualNumber->agent_id);
                                        $agent_name = $User ? $User->vchr_user_name : 'Agent Not Exist';
                                        $note = $agent_name . " has been designated as the lead via IVR";
                                        event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enqnE->pk_int_enquiry_id, $vendorId));
                                    }
                                }
                            } elseif (request('callType') == 2) {
                                /* ----------- Assign agent vise staff assign----- */
                                try {
                                    if (request()->has('department')) {
                                        AutomationRule::departmentViseAutoAssign(request(), $enqnE, $vendorId);

                                        if ($vendorId == 2476)
                                            AutomationRule::storeDepartmentToField(request(), $enqnE, $vendorId);
                                    }
                                } catch (\Exception $e) {
                                    \Log::info($e->getMessage());
                                }
                                /* -----------ENd automation ------------- */
                                if (request('Direction') == 'Inbound') {
                                    if (!$enqnE->staff_id && $this->mainUser->sticky_agent == 1) {

                                        $agent = User::select('vchr_user_name', 'pk_int_user_id')->where('extension', substr(request()->DestinationNumber, -10))->first();
                                        if ($agent) {
                                            $enqnE->staff_id = $agent->pk_int_user_id;
                                            $enqnE->assigned_date = Carbon::today();
                                            $enqnE->save();
                                            $agent_name = $agent ? $agent->vchr_user_name : 'Agent Not Exist';
                                            $note = $agent_name . " has been designated as the lead via IVR";
                                            event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enqnE->pk_int_enquiry_id, $vendorId));
                                        }
                                        $stfId = $agent->pk_int_user_id;
                                    }
                                }
                            }

                            if ($enq) {
                                $log_type_label = null;
                                $staff = User::select('vchr_user_name')->find($enq->staff_id);
                                $assignTo = $staff ? "
                      
🦁 Assigned to : " . $staff->vchr_user_name : '';
                                $customer_name = $enq->vchr_customer_name ? $enq->vchr_customer_name : "No Customer Name";
                                $followup = EnquiryFollowup::where('enquiry_id', $enq->pk_int_enquiry_id)->orderBy('id', 'DESC')->first();

                                /* Enquiry followup */
                                if ($followup) {
                                    $created_by = User::select('vchr_user_name')->find($enq->created_by);
                                    $uname = $created_by ? $created_by->vchr_user_name : 'Agent';
                                    $log_type_label = $this->followup($followup, $enq);
                                    $activity = $log_type_label ? "
                          
📢 Last Activity : " . $log_type_label : '';
                                } else {
                                    $activity = '';
                                }
                                $purpose = EnquiryPurpose::where('pk_int_purpose_id', $enq->fk_int_purpose_id)->first();
                                $vchrpurpose = $purpose ? "
                            
✳️ Purpose : " . $purpose->vchr_purpose : '';
                            } else {
                                $customer_name = "New Customer";
                                $activity = '';
                                $vchrpurpose = '';
                                $assignTo = '';
                            }
                            $feedback_status = $enq ? $enq->feedback_status : null;
                            if ($feedback_status) {
                                $status = FeedbackStatus::where('pk_int_feedback_status_id', $feedback_status)->select('vchr_status')
                                    ->first();
                                if ($status) {
                                    $lead_status = $status->vchr_status;
                                } else {
                                    $lead_status = "New Status";
                                }
                            } else {
                                $lead_status = "New Status";
                            }

                            $content1 = [
                                'text' => "🔥 You have got a new Call.

📱 DID : " . request()->DisplayNumber . " 

⏰ " . Carbon::now()->format('d M Y h:i A') . "

👤 Name :" . $customer_name . "

📱 Number : +" . request()->SourceNumber .

                                    $assignTo . "

🚀 Status : " . $lead_status .

                                    $vchrpurpose .

                                    $activity,
                                'buttons' => [[
                                    'text' => '🔆 Click to Chat in WhatsApp',
                                    'url' => 'https://wa.me/' . str_replace("+", "", request()->SourceNumber)
                                ]]
                            ];
                            $content2 = "🔥 You have got a new Call.
                        
📱 DID : " . request()->DisplayNumber . " 

⏰ " . Carbon::now()->format('d M Y h:i A') . "

👤 Name :" . $customer_name . "

📱 Number : +" . request()->SourceNumber .

                                $assignTo . "

🚀 Status : " . $lead_status .

                                $vchrpurpose .

                                $activity;

                            $notification_title = "IVR Call from " . $customer_name . "(+" . request()->SourceNumber . ")";
                            $notification_description = $content2;
                            $notification_data = [
                                "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                                "sound" => "default",
                                "page" => "enquiry_details",
                                "id" => (string)$insertCrm
                            ];
                            $idAg = $enqnE?->staff_id ?? $enq?->staff_id;

                            $dataSend['message'] = $content1;
                            $dataSend['user_id'] = $idAg ?? $vendorId;
                            $dataSend['page'] = 'ivr';
                            $dataSend['enquiry'] = $enqnE ?? $enq;
                            $dataSend['did'] = request()->DisplayNumber;
                            $dataSend['name'] = $customer_name;
                            $dataSend['staff'] = $assignTo;
                            $dataSend['mobile'] = "+" . request()->SourceNumber;
                            $this->notifications->notifications($this->from, $this->to, $this->subject, $this->name, $content1, $content2, $this->logo, $this->attachment, $this->telegramId, $vendorId, $this->mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, $dataSend);
                            $token_users = $getVirtualNumber->agent_id ? [$getVirtualNumber->agent_id, $getVirtualNumber->fk_int_user_id] : [$getVirtualNumber->fk_int_user_id];
                            $agent_staffs = $getVirtualNumber->agent_id ? AgentStaff::where('agent_id', $getVirtualNumber->agent_id)->pluck('staff_id')->toArray() : [];
                            $noti_user_ids = array_merge($token_users, $agent_staffs);

                            /** Send ivr notification to web */
                            // try{
                            //     if(request()->has('callType') && request('callType') == '1')
                            //         $this->sendIvrNotificationToWeb($dataSend);
                            // }catch(\Exception $e){
                            //     \Log::info($e->getMessage());
                            // }
                            // /** end Send ivr notification to web */

                            /* start Agent notification  */
                            if ($enq) {
                                $NewEnq = $enqnE;
                                if ($NewEnq) {
                                    $idAg = $NewEnq->staff_id;
                                } else {
                                    $idAg = $enq->staff_id;
                                }
                                $agentNotif = User::select('vchr_user_name', 'vchr_logo', 'telegram_id', 'vchr_user_mobile')->find($idAg);
                                if ($agentNotif) {
                                    if ($agentNotif->email != null) {
                                        try {
                                            $name = $agentNotif->vchr_user_name;
                                            $logo = $agentNotif->vchr_logo;
                                            $telegramId = $agentNotif->telegram_id;
                                            $mobileNumber = $agentNotif->vchr_user_mobile;
                                            $this->notifications->notifications($this->from, null, $this->subject, $name, $content1, $content2, $logo, $this->attachment, $telegramId, $vendorId, $mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, null);
                                        } catch (\Exception $exp) {

                                        }
                                    }
                                }
                            }
                            /* end Agent notification  */

                            /* ----------Notification---------- */
                            try {
                                $result = Notifications::getUserTokens($idAg);
                                if ($result && request('callType') == 0 && Variables::checkEnableSettings('ivr-app-notification', $vendorId)) {
                                    SendNotification::dispatch($result, $notification_title, $notification_description, $notification_data);
                                }
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                                \Log::info('ivr app notification');
                            }
                            /* ----------End Notification---------- */

                            /**
                             * Function for cloud telephony master
                             */
                            $insert = $this->pushCloudCall($vendorId, request(), 'Incoming call', 2);
                            $DStaffid = null;
                            if (request()->has('DestinationNumber')) {
                                $mob = request('DestinationNumber');
                                $User = User::select('pk_int_user_id')->where('mobile', substr($mob, -10))->first();
                                $DStaffid = $User ? $User->pk_int_user_id : null;
                            }


                            //show timeline
                            $enquiryfollowups = new EnquiryFollowup();
                            $enquiryfollowups->enquiry_id = $insertCrm;
                            $enquiryfollowups->name = "Incoming call";
                            $enquiryfollowups->note = request()->DisplayNumber;
                            $enquiryfollowups->log_type = EnquiryFollowup::IVR;
                            $enquiryfollowups->response = CallMaster::BONVOICE;
                            $enquiryfollowups->task_type = 'IVR';
                            $enquiryfollowups->task_id = $insert->id;
                            $enquiryfollowups->created_by = $DStaffid ? $DStaffid : ($idAg ?? $vendorId);
                            $enquiryfollowups->save();

                            return response()->json(['message' => "Incoming call landed on server", 'status' => 'success']);
                        } else {
                            // /** end Send ivr notification to web */
                        }


                    } else {
                        return response()->json(['message' => "Plan Expired", 'status' => 'fail']);
                    }
                } else if ($existDisplayNumber) {
                    if (request()->has('SourceNumber') && request()->has('DisplayNumber') && !request()->recording_URL) {

                        $update = CallMaster::where('call_uuid', request()->callID)->first();
                        $update->agent_number = request()->DestinationNumber;
                        $update->events = "Call answered by an agent";
                        $update->save();

                        //Notifications
                        $vendorId = $existDisplayNumber->vendor_id;
                        $this->notificationInitialisation($vendorId);

                        $agent = User::select('vchr_user_name', 'pk_int_user_id')->where('mobile', substr(request()->DestinationNumber, -10))/* ->where('parent_user_id',$vendorId) */ ->first();//->where('parent_user_id',$vendorId)->where('int_role_id',User::STAFF)->first();
                        $agent_name = $agent ? $agent->vchr_user_name : 'Agent Not Exist';
                        $enq = Enquiry::where('fk_int_user_id', User::getVendorIdApi($vendorId))
                            ->where(function ($where) use ($existDisplayNumber) {
                                $where->where('vchr_customer_mobile', $existDisplayNumber->caller_number)
                                    ->orWhere('vchr_customer_mobile', "+" . $existDisplayNumber->caller_number);
                            })->orderBy('vchr_customer_name', 'ASC')->first();
                        if ($enq) {
                            $customer_name = $enq->vchr_customer_name ? $enq->vchr_customer_name : "No Customer Name";
                            if (!$enq->staff_id && $agent && $this->mainUser->sticky_agent == 1) {
                                $enquiry = Enquiry::find($enq->pk_int_enquiry_id);
                                if ($agent) {
                                    $enquiry->staff_id = $agent->pk_int_user_id;
                                    $enquiry->assigned_date = Carbon::today();
                                    $enquiry->save();

                                    $note = $agent_name . " has been designated as the lead via IVR";
                                    event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enq->pk_int_enquiry_id, $vendorId));
                                }
                                $stfId = $agent->pk_int_user_id;
                            } else {
                                $stfId = $enq->staff_id;
                            }
                        } else
                            $customer_name = "New Customer";
                        $content1 = "🔅 Hey , IVR call is answered by an Agent. 🔅

Customer Name : " . $customer_name . "
Customer Number : +" . $existDisplayNumber->caller_number . "

Agent Name : " . $agent_name . "

Date and Time : " . Carbon::now();
                        $content2 = $content1;

                        try {

                            $dataSend['message'] = $content1;
                            $dataSend['user_id'] = $stfId ?? $vendorId;
                            $dataSend['page'] = 'ivr';
                            $dataSend['enquiry'] = $enq;
                            $dataSend['did'] = request()->DisplayNumber;
                            $dataSend['name'] = $customer_name;
                            $dataSend['staff'] = $agent_name;
                            $this->notifications->notifications($this->from, $this->to, $this->subject, $this->name, $content1, $content2, $this->logo, $this->attachment, $this->telegramId, $vendorId, $this->mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, null);
                            //-----------------

                            /* start Agent notification  */
                            if ($agent) {
                                $agentNotif = User::select('pk_int_user_id', 'vchr_user_name', 'vchr_logo', 'telegram_id', 'vchr_user_mobile')->find($agent->pk_int_user_id);
                                if ($agentNotif) {
                                    if ($agentNotif->email != null) {
                                        try {
                                            $name = $agentNotif->vchr_user_name;
                                            $logo = $agentNotif->vchr_logo;
                                            $telegramId = $agentNotif->telegram_id;
                                            $mobileNumber = $agentNotif->vchr_user_mobile;
                                            $this->notifications->notifications($this->from, null, $this->subject, $name, $content1, $content2, $logo, $this->attachment, $telegramId, $vendorId, $mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, null);
                                        } catch (\Exception $exp) {

                                        }
                                    }
                                }
                            }
                            /* end Agent notification  */

                        } catch (\Exception $exp) {

                        }

                        return response()->json(['message' => "Call answered by an agent", 'status' => 'success']);

                    } else if (request()->has('DisplayNumber') && !request()->SourceNumber && !request()->ResourceURL) {
                        /**
                         * Function for cloud telephony master
                         */
                        $this->updateCloudCall(request(), "Call Ended", 2);

                        return response()->json(['message' => "When a call is disconnected", 'status' => 'success']);
                    } else {
                        /**
                         * Function for cloud telephony master
                         */
                        $status = request()->CallDuration > 0 ? "Call Connected" : "Missed call";
                        $update = $this->updateCloudCall(request(), $status, 2);

                        if ($update) {


                            if (!request()->DestinationNumber || request()->DestinationNumber == null || request()->DestinationNumber == 'Nil' || request()->DestinationNumber == 'None') {
                                //Notifications
                                $vendorId = $update->vendor_id;
                                $vendor_id = User::getVendorIdApi($vendorId);
                                //Auto Assign
                                $enq = Enquiry::where('fk_int_user_id', $vendor_id)
                                    ->where(function ($where) use ($update) {
                                        $where->where('vchr_customer_mobile', $update->caller_number)
                                            ->orWhere('vchr_customer_mobile', "+" . $update->caller_number);
                                    })->orderBy('vchr_customer_name', 'ASC')->first();
                                if ($enq && !$enq->staff_id) {
                                    $ex_enquiry = Enquiry::find($enq->pk_int_enquiry_id);
                                    $auto_agent = AssignAgent::where('vendor_id', $vendor_id)->first();
                                    if ($auto_agent) {
                                        $ex_enquiry->staff_id = $auto_agent->agent_id;
                                        $ex_enquiry->assigned_date = Carbon::today();
                                        $ex_enquiry->save();

                                        $User = User::find($auto_agent->agent_id);
                                        $agent_name = $User ? $User->vchr_user_name : 'Agent Not Exist';
                                        $note = $agent_name . " has been designated as the lead via IVR";
                                        event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $ex_enquiry->pk_int_enquiry_id, $vendorId));
                                    }
                                }

                                //Notifications
                                $vendorId = $existDisplayNumber->vendor_id;
                                $vendor_id = User::getVendorIdApi($vendorId);
                                $this->notificationInitialisation($vendorId);

                                $enq = Enquiry::where('fk_int_user_id', $vendor_id)
                                    ->where(function ($where) use ($update) {
                                        $where->where('vchr_customer_mobile', $update->caller_number)
                                            ->orWhere('vchr_customer_mobile', "+" . $update->caller_number);
                                    })->orderBy('vchr_customer_name', 'ASC')->first();
                                if ($enq)
                                    $customer_name = $enq->vchr_customer_name ? $enq->vchr_customer_name : "No Customer Name";
                                else
                                    $customer_name = "New Customer";
                                $content1 = "Hey , IVR Missed Call.
Customer Name : " . $customer_name . "
Customer Number : " . $update->caller_number . "
Date and Time : " . Carbon::now();
                                $content2 = $content1;
                                //Task Creation
                                $userId = null;
                                $task = [
                                    'name' => 'Call Back ' . ($customer_name == "New Customer" || $customer_name == "No Customer Name" ? $update->caller_number : $customer_name),
                                    'description' => 'Missed call through IVR',
                                    'scheduled_date' => Carbon::now()->addMinutes(1)->format('Y-m-d H:i:s'),
                                    'task_category_id' => 2,
                                    'assigned_to' => $enq->staff_id ? $enq->staff_id : ($userId ? $userId : null),
                                    'assigned_by' => $vendorId,
                                    'vendor_id' => $vendorId,
                                    'enquiry_id' => $enq->pk_int_enquiry_id,
                                    'comment' => '',
                                    'status' => 0,
                                ];

                                /* Bazani client */
                                if ($vendorId != Variables::BAZANI_USER_ID)
                                    Task::create($task);

                                //
                                try {
                                    $dataSend['message'] = $content1;
                                    $dataSend['user_id'] = $enq->staff_id ? $enq->staff_id : ($userId ? $userId : $vendorId);
                                    $dataSend['page'] = 'ivr';
                                    $dataSend['enquiry'] = $enq;
                                    $dataSend['did'] = request()->DisplayNumber ?? '';
                                    $dataSend['name'] = $customer_name;
                                    $usr = User::find($enq->staff_id ? $enq->staff_id : ($userId ? $userId : null));
                                    $dataSend['staff'] = $usr ? $usr->vchr_user_name : '';
                                    $this->notifications->notifications($this->from, $this->to, $this->subject, $this->name, $content1, $content2, $this->logo, $this->attachment, $this->telegramId, $vendorId, $this->mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, null);
                                    //-----------------

                                    /* start Agent notification  */
                                    if ($ex_enquiry) {
                                        $agentNotif = User::select('pk_int_user_id', 'vchr_user_name', 'vchr_logo', 'telegram_id', 'vchr_user_mobile')->find($ex_enquiry->staff_id);
                                        if ($agentNotif) {
                                            if ($agentNotif->email != null) {
                                                try {
                                                    $name = $agentNotif->vchr_user_name;
                                                    $logo = $agentNotif->vchr_logo;
                                                    $telegramId = $agentNotif->telegram_id;
                                                    $mobileNumber = $agentNotif->vchr_user_mobile;
                                                    $dataSend['message'] = $content1;
                                                    $dataSend['user_id'] = $agentNotif->pk_int_user_id ?? $vendorId;
                                                    $dataSend['page'] = 'ivr';
                                                    $dataSend['enquiry'] = $ex_enquiry;
                                                    $dataSend['did'] = request()->DisplayNumber ?? '';
                                                    $dataSend['name'] = $customer_name;
                                                    $usr = User::find($ex_enquiry->staff_id ? $ex_enquiry->staff_id : ($userId ? $userId : null));
                                                    $dataSend['staff'] = $usr ? $usr->vchr_user_name : '';
                                                    $this->notifications->notifications($this->from, null, $this->subject, $name, $content1, $content2, $logo, $this->attachment, $telegramId, $vendorId, $mobileNumber, $this->defaultRouteAdmin, $this->defaultSenderIdAdmin, null);
                                                } catch (\Exception $exp) {

                                                }
                                            }
                                        }
                                    }
                                    /* end Agent notification  */
                                } catch (\Exception $exp) {

                                }

                            }

                        }
                        return response()->json(['message' => " CDR push at the end of the call", 'status' => 'success']);
                    }

                } else {
                    return response()->json(['message' => "Please Add Virtual Number", 'status' => 'fail']);
                }
            } else {
                \Log::info('error-bonvoice input');
                \Log::info($input);
                \Log::info('validation error-' . $validator->messages());
                return response()->json(['message' => $validator->messages(), 'status' => 'fail']);

            }

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage() . ' (Line: ' . $e->getLine() . ')';
            return response()->json(['msg' => $errorMessage, 'status' => 'fail']);
        }
    }

    /**
     * Click to call function for bonvoice
     */
    public function clickToCallBonvoice($request, $user)
    {
        Log::info('Received click to call request to bonvoice', ['request' => $request->all(), 'user_id' => $request->user()->pk_int_user_id]);

        $this->user = $user;
        $this->userId = $this->user->pk_int_user_id;


        $validation = Validator::make($request->all(), [
            'enquiry_id' => ['required'],
        ]);

        if ($validation->fails()) {
            return response()->json(['msg' => $validation->errors(), 'status' => 'fail'], 200);
        }

        Log::withContext([
            'enquiry_id' => $request->enquiry_id,
            'user_id' => $this->userId,
        ])->info('bonvoice click to call request received');

        $enquiry = Enquiry::query()->find($request->enquiry_id);
        $data = [];
        if ($enquiry instanceof Enquiry) {
            $group = GroupUsers::query()->where([
                'fk_int_user_id' => $enquiry->fk_int_user_id,
            ])->where(function ($where) use ($enquiry) {
                $where->where('int_mobile', $enquiry->mobile_no)
                    ->orWhere('enquiry_id', $enquiry->pk_int_enquiry_id);
            })->first();

            $user_id = $this->user->int_role_id == User::STAFF ? $this->user->parent_user_id : $this->user->pk_int_user_id;

            if ($group && $group->ivr) {
                $ext = true;
                $ivr_number = $group->ivr;
            } else {
                $ext = false;
                $ivr_number = $enquiry->did_number;
            }

            if ($ivr_number) {
                $base_url = 'https://backend.pbx.bonvoice.com/autoDialManagement/autoCallBridging/';

                Log::info('Bonvoice click to call, found virtual number', ['ivr_number' => $ivr_number->vchr_virtual_number,
                    'uid' => $ivr_number->uid,
                    'channel_id' => $ivr_number->channel_id]);

                Log::info('Bonvoice preparing for the call');

                $autocallType = 3;
                $legADialAttempts = 1;
                $legBDialAttempts = 1;
                $eventID = $enquiry->pk_int_enquiry_id;
                $token = $ivr_number->pin ?? '2ad6e8be28e96916f50540cfcb27eef8f48de1eb';
                $phoneNumber = str_replace("+", "", $request->phone_number ?? $enquiry->vchr_customer_mobile);

                $body = [
                    'autocallType' => $autocallType,
                    'destination' => '0' . $request->user()->mobile,
                    'legACallerID' => $ivr_number->uid,
                    'legAChannelID' => $ivr_number->channel_id,
                    'legADialAttempts' => $legADialAttempts,
                    'legBDestination' => $phoneNumber,
                    'legBChannelID' => $ivr_number->channel_id,
                    'legBCallerID' => $ivr_number->uid,
                    'legBDialAttempts' => $legBDialAttempts,
                    'eventID' => $eventID,
                ];

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $base_url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($body),
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Token ' . $token,
                        'Content-Type: application/json'
                    ),
                ));

                $response = curl_exec($curl);
                curl_close($curl);

                $obj = json_decode($response, true);
                Log::info('Reponse from the bonvoice click to call', ['body' => $body, 'response' => $obj]);

                if (!isset($obj['responseType'])) {
                    dispatch(new NotifyWhenBonvoiceClickToCallFailed(userId: $this->user->pk_int_user_id,
                        vendorId: $enquiry->fk_int_user_id,
                        customerPhone: new PhoneNumber(Str::start($phoneNumber, '+')),
                        body: $body,
                        response: $obj));
                }

                if (isset($obj['responseType'])) {
                    if ($obj['responseType'] == 'Success') {
                        $transfer_log = new \App\BackendModel\IvrTransferLog;
                        $transfer_log->user_id = $user_id;
                        $transfer_log->virtual_number = $ivr_number->vchr_virtual_number;
                        $transfer_log->agent_number = str_replace("+", "", $request->user()->vchr_user_mobile);
                        $transfer_log->customer_number = str_replace("+", "", $enquiry->vchr_customer_mobile);
                        $transfer_log->customer_name = $enquiry->vchr_customer_name;
                        $transfer_log->status = 0;
                        if ($request->expire_in && $request->expire_in != '')
                            $transfer_log->expire_at = Carbon::now()->addMinutes($request->expire_in)->format('Y-m-d- H:i:s');
                        $transfer_log->save();
                        $transfer_log->track_id = 'GLITR000' . $transfer_log->id;
                        $transfer_log->save();

                        $data['phone_number'] = $ivr_number->vchr_virtual_number;
                    } elseif ($obj['responseCode'] == 910) {
                        return response()->json(['data' => 'No channel', 'message' => 'Ivr channel unavailable', 'status' => 0], 200);
                    } elseif ($obj['has_credit'] == 'no') {
                        return response()->json(['data' => 'No balance', 'message' => 'You have no credit in your account', 'status' => 0], 200);
                    } elseif ($obj['responseType'] == 'Error') {
                        return response()->json(['data' => $obj['responseDescription'], 'message' => 'failed', 'status' => 0], 200);
                    }
                } elseif (isset($obj['has_credit']) && $obj['has_credit'] == 'no') {
                    return response()->json(['data' => 'No balance', 'message' => 'You have no credit in your account', 'status' => 0], 200);
                } elseif (isset($obj['error'])) {
                    return response()->json(['data' => 'No balance', 'message' => $obj['error'], 'status' => 0], 200);
                } elseif (isset($obj['detail'])) {
                    return response()->json(['data' => 'No balance', 'message' => $obj['detail'], 'status' => 0], 200);
                } else {
                    return response()->json(['data' => $obj['responseDescription'], 'message' => 'failed', 'status' => 0], 200);
                }
            } else {
                return response()->json(['data' => [], 'message' => 'IVR number not exist', 'status' => 0], 200);
            }
        } else {
            return response()->json(['data' => [], 'message' => 'Enquiry not exist', 'status' => 0], 200);
        }
        return response()->json(['data' => $data, "message" => "Success", 'status' => 1], 200);
    }
}
