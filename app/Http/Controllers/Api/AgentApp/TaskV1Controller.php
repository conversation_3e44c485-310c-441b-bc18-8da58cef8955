<?php

namespace App\Http\Controllers\Api\AgentApp;

use App\AgentStaff;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\Common\Application;
use App\Common\Notifications;
use App\Common\Variables;
use App\Deal;
use App\DealTask;
use App\DealTaskHistory;
use App\Events\CreateFollowup;
use App\Events\LeadAssigned;
use App\Events\DealTaskCompleted;
use App\Events\TaskCompleted;
use App\TaskCategory;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Http\Controllers\Api\AgentApp\Controller;
use App\Jobs\SendNotification;
use App\Task;
use App\TaskHistory;
use App\Traits\EnquiryTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\DealActivity;
use App\Events\TaskAssigned;
use App\Reason;
use Getlead\Campaign\Models\CampaignLead;
use App\Traits\DealTrait;
use Getlead\Campaign\Models\LeadCampaign;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;

class TaskV1Controller extends Controller
{
    protected $userId;
    protected $vendorId;
    protected $user;
    protected $applicationObj;
    protected $vendor;
    protected $roleId;

    use EnquiryTrait, DealTrait;

    public function __construct()
    {
        $this->middleware('jwt.auth');
        $this->applicationObj = new Application();
        $this->user = JWTAuth::parseToken()->toUser();
        $this->userId = $this->user->pk_int_user_id;
        $this->roleId = $this->user->int_role_id;
        $this->vendorId = $this->applicationObj->getVendorId($this->userId);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * Task categories api
     */
    public function taskCategories()
    {
        $default = [1, 2, 22];
        $assignedStafId = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
        $vendor_tasks = TaskCategory::where('vendor_id', $this->vendorId)
            ->orWhereIn('id', $default)
            ->select('id', 'name', 'vendor_id')
            ->get()
            ->map(function ($category) use ($assignedStafId) {
                $category['id'] = $category->id;
                $category['name'] = $category->name;
                $category['tasks_count'] = $category->tasks()->NonCampaign()
                    ->where('vendor_id', $this->vendorId)
                    ->where('status', '!=', 1)
                    ->where(function ($where) use ($assignedStafId) {
                        if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
                            $where->where(function ($where2) use ($assignedStafId) {
                                $where2->where('assigned_to', $this->userId)
                                    ->orWhereIn('assigned_to', $assignedStafId);
                            });
                        }
                    })
                    ->where(function ($q) {
                        if (request()->has('agent') && request()->filled('agent')) {
                            $agent = json_decode(request()->agent);
                            if ($agent)
                                $q->whereIn('assigned_to', $agent);
                        }
                        if (request()->has('date_from') && request()->filled('date_from')) {
                            $q->whereDate('scheduled_date', '>=', request()->date_from)
                                ->whereDate('scheduled_date', '<=', request()->date_to);
                        }
                        if (request()->has('enquiry_id')) {
                            $q->where('enquiry_id', request('enquiry_id'));
                        }
                    })->count();

                $deal_tasks_count = $category->dealTasks()
                    ->where('vendor_id', $this->vendorId)
                    ->where('status', '!=', 1)
                    ->where(function ($where) use ($assignedStafId) {
                        if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
                            $where->where(function ($where2) use ($assignedStafId) {
                                $where2->where('assigned_to', $this->userId)
                                    ->orWhereIn('assigned_to', $assignedStafId);
                            });
                        }
                    })
                    ->where(function ($q) {
                        if (request()->has('agent') && request()->filled('agent')) {
                            $agent = json_decode(request()->agent);
                            if ($agent)
                                $q->whereIn('assigned_to', $agent);
                        }
                        if (request()->has('date_from') && request()->filled('date_from')) {
                            $q->whereDate('scheduled_date', '>=', request()->date_from)
                                ->whereDate('scheduled_date', '<=', request()->date_to);
                        }
                        if (request()->has('enquiry_id')) {
                            $q->whereHas('deal', function ($q) {
                                $q->where('lead_id', request('enquiry_id'));
                            });
                        }
                    })
                    ->count();
                $category['tasks_count'] = $category['tasks_count'] + $deal_tasks_count;
                return $category;
            });

        $data['task_categories'] = $vendor_tasks;

        return $this->response(200, false, null, $data);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * Task List
     */
    public function taskList(Request $request)
    {
        Log::info('Task List from V1', $request->all());

        if ($this->vendorId == 2737) {
            try {
                JWTAuth::refresh(JWTAuth::getToken()); // Invalidate token   
            } catch (\Exception $e) {
                \Log::info('--------invalidate the user token on id 2737-------');
            }
        }

        $selectColumns = [
            'tasks.id',
            'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status',
            'task_categories.name as task_category',
            'tbl_users.vchr_user_name as assigned_user_name',
            'tasks.updated_at as updated_at'
        ];

        $dealSelectColumns = [
            'deal_tasks.id', 'deal_tasks.name',
            'deal_tasks.description', 'deal_tasks.scheduled_date',
            'deal_tasks.task_category_id', 'deal_tasks.task_order',
            'deal_tasks.assigned_to', 'deal_tasks.assigned_by',
            'deal_tasks.vendor_id',
            'deal_tasks.comment', 'deal_tasks.status',
            'task_categories.name as task_category',
            'deals.deal_name', 'deals.pk_int_deal_id',
            'deals.lead_id as enquiry_id',
            'tbl_users.vchr_user_name as assigned_user_name',
            'deal_tasks.updated_at as updated_at'];

        $assignedStafId = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
        $query = Task::where('tasks.vendor_id', $this->vendorId);
        $queryDeal = DealTask::where('deal_tasks.vendor_id', $this->vendorId);

        if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
            $query = $query->where(function ($q) use ($assignedStafId) {
                $q->where('assigned_to', $this->userId)
                    ->orWhereIn('assigned_to', $assignedStafId);
            });
            $queryDeal = $queryDeal->where(function ($q) use ($assignedStafId) {
                $q->where('assigned_to', $this->userId)
                    ->orWhereIn('assigned_to', $assignedStafId);
            });
        }

        $tasks_query = (clone $query)->select($selectColumns)->NonCampaign()
            ->join('task_categories', 'tasks.task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_users', 'tasks.assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->where(function ($q) use ($request) {
                if ($request->filled('agent')) {
                    $q->whereIn('tasks.assigned_to', json_decode($request->agent, true));
                }
                if ($request->filled('enquiry_id')) {
                    $q->where('tasks.enquiry_id', $request->enquiry_id);
                }
                if ($request->filled('task_category')) {
                    $q->whereIn('tasks.task_category_id', json_decode($request->task_category, true));
                }
                if ($request->filled('date_from')) {
                    $q->whereDate('tasks.scheduled_date', '>=', $request->date_from)
                        ->whereDate('tasks.scheduled_date', '<=', $request->date_to);
                }
            });

        $deal_tasks_query = (clone $queryDeal)->select($dealSelectColumns)
            ->join('task_categories', 'deal_tasks.task_category_id', '=', 'task_categories.id')
            ->leftjoin('deals', 'deal_id', '=', 'deals.pk_int_deal_id')
            ->leftjoin('tbl_users', 'deal_tasks.assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->where(function ($q) use ($request) {
                if ($request->filled('agent')) {
                    $q->whereIn('deal_tasks.assigned_to', json_decode($request->agent, true));
                }
                if ($request->filled('enquiry_id')) {
                    $q->where('deals.lead_id', $request->enquiry_id);
                }
                if ($request->filled('task_category')) {
                    $q->whereIn('deal_tasks.task_category_id', json_decode($request->task_category, true));
                }
                if ($request->filled('date_from')) {
                    $q->whereDate('deal_tasks.scheduled_date', '>=', $request->date_from)
                        ->whereDate('deal_tasks.scheduled_date', '<=', $request->date_to);
                }
            });

        // Seperate search implementation
        if ($request->filled('keyword')) {
            $tasks_query->where(function ($query) use ($request) {
                $query->where('tasks.name', 'like', '%' . $request->keyword . '%')
                    ->orWhere(function ($q) use ($request) {
                        $q->whereHas('enquiry', function ($q) use ($request) {
                            $q->where(function ($query) use ($request) {
                                $query->where('vchr_customer_name', 'like', '%' . $request->keyword . '%')
                                    ->orWhere('vchr_customer_mobile', 'like', '%' . $request->keyword . '%');
                            });
                        });
                    });
            });

            $deal_tasks_query->where(function ($query) use ($request) {
                $query->where('deal_tasks.name', 'like', '%' . $request->keyword . '%')
                    ->orWhere(function ($q) use ($request) {
                        $q->orWhereHas('deal', function ($q) use ($request) {
                            $q->where('deals.deal_name', 'like', '%' . $request->keyword . '%');
                        });
                    });
            });
            $lead_tasks = $tasks_query->orderBy('tasks.id', 'DESC')->take(15)->get();
            $deal_tasks = $deal_tasks_query->orderBy('deal_tasks.id', 'DESC')->take(15)->get();
            $combinedCollection = $lead_tasks->merge($deal_tasks);

            $data['search_result'] = $combinedCollection->sortByDesc('scheduled_date')->values();

            return $this->response(200, false, null, $data);
        }

        // $tasks_completed_q = clone($tasks_query);
        $tasks_overdue_q = clone($tasks_query);
        $tasks_pending_q = clone($tasks_query);

        // $deal_tasks_completed_q = clone($deal_tasks_query);
        $deal_tasks_overdue_q = clone($deal_tasks_query);
        $deal_tasks_pending_q = clone($deal_tasks_query);

        if ($request->date) {
            $tasks = $tasks_query->whereBetween('scheduled_date', [$request->date . ' 00:00:00', $request->date . ' 23:59:59'])
                ->where('status', 0)->orderBy('tasks.id', 'DESC')
                ->take(15)
                ->get();
            $dealTasks = $deal_tasks_query->whereBetween('scheduled_date', [$request->date . ' 00:00:00', $request->date . ' 23:59:59'])
                ->where('status', 0)
                ->orderBy('deal_tasks.id', 'DESC')
                ->take(15)
                ->get();

            $combinedCollection = $tasks->merge($dealTasks);
            $data['tasks'] = $combinedCollection->sortBy('updated_at')->values();
        } else {
            $tasks_overdue = $tasks_overdue_q->where('status', 0)
                ->whereDate('scheduled_date', '<', Carbon::today())
                ->orderBy('tasks.id', 'DESC')
                ->get();
            $deal_tasks_overdue = $deal_tasks_overdue_q->where('status', 0)
                ->whereDate('scheduled_date', '<', Carbon::today())
                ->orderBy('deal_tasks.id', 'DESC')
                ->get();

            $combinedOverDue = new Collection();
            $combinedOverDue = $combinedOverDue->merge($tasks_overdue)->merge($deal_tasks_overdue);
            $data['tasks_overdue'] = $combinedOverDue->sortByDesc('updated_at')->values();

            $tasks_pending = $tasks_pending_q->where('status', 0)
                ->whereDate('scheduled_date', '>=', Carbon::today())
                ->orderBy('tasks.id', 'DESC')
                ->take(15)
                ->get();
            $deal_tasks_pending = $deal_tasks_pending_q->where('status', 0)
                ->whereDate('scheduled_date', '>=', Carbon::today())
                ->orderBy('deal_tasks.id', 'DESC')
                ->take(15)
                ->get();
            $combinedpending = new Collection();
            $combinedpending = $combinedpending->merge($tasks_pending)->merge($deal_tasks_pending);
            $data['tasks_pending'] = $combinedpending->sortByDesc('updated_at')->values();
        }

        $data['is_campaign'] = (clone $query)->select('status', 'id', 'campaign_id')->whereNotNull('campaign_id')->where('status', 0)->first() ? true : false;

        return $this->response(200, false, null, $data);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Task complete api
     */
    public function markAsDone(Request $request)
    {
        $validate_fields = [
            'task_id' => ['required'],
        ];
        $validate_messages = [
            'task_id.required' => 'Task id is required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        try {
            DB::beginTransaction();
            if (request('task_type') == 'lead')
                $task = Task::find($request->task_id);
            else
                $task = DealTask::find($request->task_id);

            if ($task) {
                if (request('task_type') == 'lead') {
                    $input = ['status' => 1, 'comment' => $request->comment];
                    Task::where('id', $request->task_id)->update($input);
                    TaskHistory::create(['task_id' => $request->task_id,
                        'task_status' => 'Completed',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'comment' => $request->comment,
                        'date' => Carbon::today()
                    ]);
                    if ($task->assigned_by != $this->userId) {
                        event(new TaskCompleted($task, $this->userId));
                    }
                } else {
                    $input = ['status' => 1, 'comment' => $request->comment];
                    DealTask::where('id', $request->task_id)->update($input);
                    DealTaskHistory::create(['deal_task_id' => $request->task_id,
                        'task_status' => 'Completed',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'comment' => $request->comment,
                        'date' => Carbon::today()
                    ]);
                    DealActivity::where('task_id', $request->task_id)->update(['task_status' => 1]);
                    if ($task->assigned_by != $this->userId) {
                        event(new DealTaskCompleted($task, $this->userId));
                    }
                }


            } else {
                return $this->response(200, true, 'fail', null);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response(200, false, $exception->getMessage(), null);
        }
        DB::commit();
        return $this->response(200, false, 'success', null);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Reopen Task api
     */
    public function taskReopen(Request $request)
    {
        $validate_fields = [
            'task_id' => ['required'],
        ];
        $validate_messages = [
            'task_id.required' => 'Task id is required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        try {
            DB::beginTransaction();
            if (request('task_type') == 'lead') {
                $task = Task::find($request->task_id);
                if ($task) {
                    $input = ['status' => 0];
                    Task::where('id', $request->task_id)->update($input);
                    TaskHistory::create(['task_id' => $request->task_id,
                        'task_status' => 'Reopened',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'date' => Carbon::today()
                    ]);
                } else {
                    return $this->response(200, true, 'fail', null);
                }
            } else {
                $task = DealTask::find($request->task_id);
                if ($task) {
                    $input = ['status' => 0];
                    DealTask::where('id', $request->task_id)->update($input);
                    DealTaskHistory::create(['deal_task_id' => $request->task_id,
                        'task_status' => 'Reopened',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'date' => Carbon::today()
                    ]);
                    DealActivity::where('task_id', $request->task_id)->update(['task_status' => 0]);
                } else {
                    return $this->response(200, true, 'fail', null);
                }
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response(200, false, $exception->getMessage(), null);
        }
        DB::commit();
        return $this->response(200, false, 'success', null);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Reschedule Task api
     */
    public function taskReschedule(Request $request)
    {
        $validate_fields = [
            'task_id' => ['required'],
        ];
        $validate_messages = [
            'task_id.required' => 'Task id is required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        try {
            if (request('task_type') == 'lead') {
                $task = Task::find($request->task_id);
                if ($task) {
                    $old_date = $task->scheduled_date;
                    $task->scheduled_date = $request->scheduled_date;
                    $task->save();
                    TaskHistory::create([
                        'task_id' => $request->task_id,
                        'task_status' => 'Rescheduled',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'old_date' => ($old_date) ? $old_date : null,
                        'date' => $request->scheduled_date,
                        'comment' => $request->reason,
                    ]);
                } else {
                    return $this->response(200, true, 'fail', null);
                }
            } else {
                $task = DealTask::find($request->task_id);
                if ($task) {
                    $old_date = $task->scheduled_date;
                    $task->scheduled_date = $request->scheduled_date;
                    $task->save();
                    DealTaskHistory::create([
                        'deal_task_id' => $request->task_id,
                        'task_status' => 'Rescheduled',
                        'updated_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'old_date' => ($old_date) ? $old_date : null,
                        'date' => $request->scheduled_date,
                        'comment' => $request->reason,
                    ]);
                } else {
                    return $this->response(200, true, 'fail', null);
                }
            }
            if ($task)
                event(new TaskAssigned($task));

        } catch (\Exception $exception) {
            return $this->response(200, false, $exception->getMessage(), null);
        }
        return $this->response(200, false, 'success', null);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Delete Task
     */
    public function deleteTask(Request $request)
    {
        $validate_fields = ['task_id' => ['required'],];
        $validate_messages = ['task_id.required' => 'Task id Required'];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        if (request('task_type') == 'lead') {
            $task = Task::find($request->task_id);
            if ($task) {
                $task->delete();
                return $this->response(200, false, "success", null);
            } else {
                return $this->response(200, true, "fail", null);
            }
        } else {
            $task = DealTask::find($request->task_id);
            if ($task) {
                $task->delete();
                return $this->response(200, false, "success", null);
            } else {
                return $this->response(200, true, "fail", null);
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Task Details
     */
    public function taskDetails(Request $request)
    {
        $validate_fields = ['task_id' => ['required']];
        $validate_messages = ['task_id.required' => 'Task id is required',];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        if (request('task_type') == 'lead') {
            $selectColumns = [
                'tasks.id', 'tasks.name',
                'tasks.description', 'tasks.scheduled_date',
                'tasks.task_category_id', 'tasks.task_order',
                'tasks.assigned_to', 'tasks.assigned_by',
                'tasks.vendor_id', 'tasks.enquiry_id',
                'tasks.comment', 'tasks.status', 'tasks.created_at',
                'task_categories.name as task_category',
                'tbl_enquiries.vchr_customer_name as customer_name',
                'tbl_enquiries.vchr_customer_mobile',
                'tbl_enquiries.country_code',
                'tbl_enquiries.mobile_no',
                'call_status.name as call_status',
                'tbl_users.vchr_user_name as assigned_user_name'];

            $tasks = Task::where('tasks.id', $request->task_id)
                ->where('tasks.vendor_id', $this->vendorId)
                ->with(['creator', 'history', 'history.creator'])
                ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
                ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
                ->leftjoin('call_status', 'call_status.id', '=', 'tasks.call_status_id')
                ->select($selectColumns)
                ->first();

            if ($tasks) {
                $task['created'] = [
                    "id" => 0,
                    "task_id" => $tasks->id,
                    "task_status" => "Created",
                    "comment" => "",
                    "updated_by" => $this->vendorId,
                    "vendor_id" => $this->vendorId,
                    "date" => Carbon::parse($tasks->scheduled_date)->format('d M Y g:i A'),
                    "created_at" => Carbon::parse($tasks->created_at)->format('d M Y g:i A'),
                    "updated_at" => Carbon::parse($tasks->updated_at)->format('d M Y g:i A'),
                    "deleted_at" => null,
                    "creator" => $tasks->creator
                ];
            }
        } else {
            $selectColumns = [
                'deal_tasks.id', 'deal_tasks.name',
                'deal_tasks.description', 'deal_tasks.scheduled_date',
                'deal_tasks.task_category_id', 'deal_tasks.task_order',
                'deal_tasks.assigned_to', 'deal_tasks.assigned_by',
                'deal_tasks.vendor_id', 'deal_tasks.deal_id',
                'deal_tasks.comment', 'deal_tasks.status', 'deal_tasks.created_at',
                'task_categories.name as task_category',
                'deals.deal_name', 'deals.deal_amount',
                'tbl_enquiries.pk_int_enquiry_id as enquiry_id',
                'tbl_enquiries.vchr_customer_name as customer_name',
                'tbl_enquiries.vchr_customer_mobile',
                'tbl_enquiries.country_code',
                'tbl_enquiries.mobile_no',
                'call_status.name as call_status',
                'tbl_users.vchr_user_name as assigned_user_name'];

            $tasks = DealTask::where('deal_tasks.id', $request->task_id)
                ->where('deal_tasks.vendor_id', $this->vendorId)
                ->with(['creator', 'history', 'history.creator'])
                ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
                ->leftjoin('deals', 'pk_int_deal_id', '=', 'deal_tasks.deal_id')
                ->leftjoin('tbl_enquiries', 'deals.lead_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
                ->leftjoin('call_status', 'call_status.id', '=', 'deal_tasks.call_status_id')
                ->select($selectColumns)
                ->first();

            if ($tasks) {
                $task['created'] = [
                    "id" => 0,
                    "task_id" => $tasks->id,
                    "task_status" => "Created",
                    "comment" => "",
                    "updated_by" => $this->vendorId,
                    "vendor_id" => $this->vendorId,
                    "date" => Carbon::parse($tasks->scheduled_date)->format('d M Y g:i A'),
                    "created_at" => Carbon::parse($tasks->created_at)->format('d M Y g:i A'),
                    "updated_at" => Carbon::parse($tasks->updated_at)->format('d M Y g:i A'),
                    "deleted_at" => null,
                    "creator" => $tasks->creator
                ];
            }
        }

        if ($tasks) {
            return response()->json([
                "status" => 1,
                "message" => "success",
                "data" => $tasks
            ]);
        } else {
            return response()->json([
                "status" => 1,
                "message" => "Not Found",
                "data" => null
            ]);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * day task count
     */
    public function dayTaskCount(Request $request)
    {
        $validate_fields = [
            'date' => ['required'],
        ];
        $validate_messages = [
            'date.required' => 'Date is required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $task = Task::where('vendor_id', $this->vendorId)
            ->whereDate('scheduled_date', $request->date)
            ->where('status', 0)
            ->where('task_category_id', 2)
            ->where('campaign_id', '=', NULL)
            ->where('assigned_to', $request->assigned_to ?? $this->userId)
            ->count();
        $dealtask = DealTask::where('vendor_id', $this->vendorId)
            ->whereDate('scheduled_date', $request->date)
            ->where('status', 0)
            ->where('task_category_id', 2)
            ->where('assigned_to', $request->assigned_to ?? $this->userId)
            ->count();
        $taskCount = (int)$task + (int)$dealtask;

        return $this->response(200, false, null, $taskCount);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Deal lists
     */
    public function dealList()
    {
        $assignedStafId = AgentStaff::where('agent_id', $this->userId)->pluck('staff_id')->toArray();
        $dealResponse = Deal::where(function ($query) use ($assignedStafId) {
            if ($this->roleId == Variables::STAFF && $this->user->is_co_admin == 0) {
                $query->where('agent_id', $this->userId)
                    ->orWhere('deals.created_by', $this->userId)
                    ->orWhere(function ($q) use ($assignedStafId) {
                        if ($assignedStafId)
                            $q->whereIn('deals.created_by', $assignedStafId)
                                ->orwhereIn('agent_id', $assignedStafId);
                    });
            } elseif ($this->roleId == Variables::USER || $this->user->is_co_admin == 1) {
                $query->where('vendor_id', $this->vendorId);
            }
        })->leftJoin('tbl_enquiries', 'tbl_enquiries.pk_int_enquiry_id', '=', 'deals.lead_id')
            ->where(function ($q) {
                if (request()->filled('search_key')) {
                    $searchTerm = request('search_key');
                    $q->where(function ($query) use ($searchTerm) {
                        $query->where('tbl_enquiries.vchr_customer_mobile', $searchTerm)
                            ->orwhere('tbl_enquiries.vchr_customer_name', 'LIKE', '%' . $searchTerm . '%')
                            ->orwhere('tbl_enquiries.vchr_customer_company_name', 'LIKE', '%' . $searchTerm . '%')
                            ->orwhere('tbl_enquiries.mobile_no', 'LIKE', '%' . $searchTerm . '%')
                            ->orWhere('deals.deal_name', 'LIKE', '%' . $searchTerm . '%')
                            ->orWhere('deals.deal_amount', 'LIKE', '%' . $searchTerm . '%');
                    });
                }
            })
            ->select('pk_int_deal_id as id', 'deal_name as name')->get();

        if ($dealResponse) {
            return $this->response(200, false, 'success', $dealResponse);
        } else {
            return $this->response(200, true, 'Not Found', null);
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * submit call feedbacks
     */
    public function submitCallFeedBack(Request $request)
    {
        Log::info('a task feedback has been submitted', ['payload' => $request->all()]);

        if (request('task_type') == 'lead') {
            $validate_fields = [
                'enquiry_id' => ['required'],
            ];
            $validate_messages = [
                'enquiry_id.required' => 'Enquiry id is required',
            ];
            if ($request->enquiry_status_id && ($request->enquiry_status_id == 307 || ($request->has('type') && $request->type >= 0))) {
                $validate_fields['type'] = ['required'];
                $validate_messages['type.required'] = 'Type is required';
                if ($request->type == 0) {
                    $validate_fields['schedule_date_time'] = ['required'];
                    $validate_messages['schedule_date_time.required'] = 'Schedule Date and Time is required';
                }
            }
        } else {
            $validate_fields = [
                'deal_id' => ['required'],
            ];
            $validate_messages = [
                'deal_id.required' => 'Deal id is required',
            ];
            if ($request->enquiry_status_id && ($request->enquiry_status_id == 307 || ($request->has('type') && $request->type >= 0))) {
                $validate_fields['type'] = ['required'];
                $validate_messages['type.required'] = 'Type is required';
                if ($request->type == 0) {
                    $validate_fields['schedule_date_time'] = ['required'];
                    $validate_messages['schedule_date_time.required'] = 'Schedule Date and Time is required';
                }
            }
        }

        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        DB::beginTransaction();
        try {
            $deal = Deal::where('pk_int_deal_id', $request->deal_id)->first();
            if (request('task_type') == 'deal') {
                if ($request->deal_status) {
                    $old = $deal->deal_stage_id;
                    $deal->deal_stage_id = $request->deal_status;
                    $deal->updated_at = now();
                    $deal->save();

                    request()->merge([
                        'deal_stage_id' => $request->deal_status,
                        'old_status_id' => $old
                    ]);
                    try {
                        DealTrait::activityInsert(request(), $deal);
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
            } else {
                $deal = null;
            }
            $enquiry = Enquiry::where('pk_int_enquiry_id', $request->enquiry_id)->first();
            if ($enquiry) {
                if ($request->reason_id) {
                    $enquiry->reason_id = $request->reason_id;
                }
                $enquiry->vchr_enquiry_feedback = $request->comment;
                if ($request->comment) {
                    $enquiry->lead_attension = 0;
                }

                if ((int)$enquiry->feedback_status != (int)$request->enquiry_status_id && $request->enquiry_status_id != null) {
                    $old_status_id = $enquiry->feedback_status;
                    $enquiry->feedback_status = $request->enquiry_status_id;


                    $enquiry->save();

                    Enquiry::statusChangeFunction($enquiry);

                    try {
                        event(new CreateFollowup($request->enquiry_status_id, EnquiryFollowup::TYPE_STATUS, $request->enquiry_id, $this->userId, $old_status_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }

                if ((int)$enquiry->fk_int_purpose_id != (int)$request->enquiry_purpose_id && $request->enquiry_purpose_id != null) {
                    $enquiry->fk_int_purpose_id = $request->enquiry_purpose_id;

                    try {
                        event(new CreateFollowup($request->enquiry_purpose_id, EnquiryFollowup::ENQ_PURPOSE, $request->enquiry_id, $this->userId));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }

                $enquiry->updated_at = now();
                $enquiry->save();

                if ($request->comment || $request->reason_id) {
                    try {
                        event(new CreateFollowup($request->comment, EnquiryFollowup::TYPE_NOTE, $request->enquiry_id, $this->userId));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
                if (request('task_type') == 'deal') {
                    $existing_deal_tasks = DealTask::where([
                        'deal_id' => $request->deal_id,
                        'task_category_id' => 2,
                        'status' => 0,
                        'vendor_id' => $this->vendorId,
                    ])->get();

                    if ($request->comment || $request->reason_id) {
                        $activity = new DealActivity();
                        if ($request->reason_id) {
                            $reason = Reason::where('id', $request->reason_id)->first();
                            $reason_name = $reason->name;
                            $activity->note = 'Call feedback reason : ' . ($reason_name);
                        } else {
                            $activity->note = 'Call feedback reason : ' . $request->comment;
                        }
                        $activity->log_type = DealActivity::TYPE_NOTE;
                        $activity->deal_id = $request->deal_id;
                        $activity->created_by = $this->userId;
                        $activity->save();

                        $deal->updated_at = now();
                        $deal->save();
                    }

                    if ($existing_deal_tasks->count() == 0) {
                        $input = [
                            'name' => 'Instant Call ' . $enquiry->vchr_customer_name,
                            'description' => '',
                            'scheduled_date' => Carbon::now()->format('Y-m-d H:i:s'),
                            'task_category_id' => 2,
                            'assigned_to' => $this->userId,
                            'assigned_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'deal_id' => $request->deal_id,
                            'comment' => $request->comment ?? '',
                            'status' => 1,
                            'call_status_id' => $request->call_status_id ?? 0
                        ];
                        $new_task = DealTask::create($input);
                        DealTaskHistory::create(['deal_task_id' => $new_task->id,
                            'task_status' => 'Feedback Submitted',
                            'updated_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'comment' => $request->comment,
                            'date' => Carbon::today()
                        ]);
                    }
                    foreach ($existing_deal_tasks as $existing_task) {
                        $existing_task1 = DealTask::find($existing_task->id);
                        $existing_task1->status = 1;
                        $existing_task1->comment = $request->comment;
                        if ($request->call_status_id) {
                            $existing_task1->call_status_id = $request->call_status_id;
                        }
                        $existing_task1->save();
                        DealTaskHistory::create(['deal_task_id' => $existing_task->id,
                            'task_status' => 'Feedback Submitted',
                            'updated_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'comment' => $request->comment,
                            'date' => Carbon::today()
                        ]);
                        if ($existing_task1->assigned_by != $this->userId) {
                            event(new DealTaskCompleted($existing_task1, $this->userId));
                        }
                    }
                } else {
                    if ($request->has('campaign_id')) {
                        $existing_tasks = Task::query()->where([
                            'enquiry_id' => $request->enquiry_id,
                            'task_category_id' => 2,
                            'status' => 0,
                            'campaign_id' => $request->campaign_id,
                            'vendor_id' => $this->vendorId,
                        ])->get();
                    } else {
                        $existing_tasks = Task::query()->where([
                            'enquiry_id' => $request->enquiry_id,
                            'task_category_id' => 2,
                            'status' => 0,
                            'campaign_id' => null,
                            'vendor_id' => $this->vendorId,
                        ])->get();
                    }

                    if (!$request->has('campaign_id') && $existing_tasks->count() == 0) {
                        $input = [
                            'name' => 'Instant Call ' . $enquiry->vchr_customer_name,
                            'description' => '',
                            'scheduled_date' => Carbon::now()->format('Y-m-d H:i:s'),
                            'task_category_id' => 2,
                            'assigned_to' => $this->userId,
                            'assigned_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'enquiry_id' => $request->enquiry_id,
                            'comment' => $request->comment ?? '',
                            'status' => 1,
                            'call_status_id' => $request->call_status_id ?? null,
                            'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                        ];
                        $new_task = Task::query()->create($input);
                        TaskHistory::create(['task_id' => $new_task->id,
                            'task_status' => 'Feedback Submitted',
                            'updated_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'comment' => $request->comment,
                            'date' => Carbon::today()
                        ]);
                    }

                    foreach ($existing_tasks as $existing_task) {

                        $existing_task1 = Task::find($existing_task->id);
                        $existing_task1->status = 1;
                        $existing_task1->comment = $request->comment;
                        if ($request->call_status_id) {
                            $existing_task1->call_status_id = $request->call_status_id;
                        }
                        $existing_task1->save();
                        TaskHistory::create(['task_id' => $existing_task->id,
                            'task_status' => 'Feedback Submitted',
                            'updated_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'comment' => $request->comment,
                            'date' => Carbon::today()
                        ]);

                        event(new CreateFollowup('Feedback Submitted', EnquiryFollowup::TASK_COMPLETED, $request->enquiry_id, $this->userId, null, null, null, null, null, $existing_task->id));
                        if ($existing_task1->assigned_by != $this->userId && !$request->campaign_id) {
                            event(new TaskCompleted($existing_task1, $this->userId));
                        }
                    }
                }

                if (($request->enquiry_status_id == 307 || ($request->has('type') && $request->type >= 0))) {
                    //Call Back (307)
                    //Parameters:
                    //type*
                    //0- Custom Date and Time
                    //1- Add 1 Hours to Current Time
                    //5- Add 5 Hours to Current Time
                    //schedule_date_time : In Case of type = 0
                    //2021-06-30 13:35:00
                    //comment
                    if ($request->type > 0)
                        $request->merge([
                            'schedule_date_time' => Carbon::now()->addHours($request->type)->format('Y-m-d H:i:s')
                        ]);
                    $input = [
                        'name' => 'Call Back ' . $enquiry->vchr_customer_name,
                        'description' => $request->comment,
                        'scheduled_date' => $request->schedule_date_time ?? now(),
                        'task_category_id' => 2,
                        'assigned_to' => $this->userId,
                        'assigned_by' => $this->userId,
                        'vendor_id' => $this->vendorId,
                        'enquiry_id' => $request->enquiry_id,
                        'comment' => $request->comment,
                        'status' => 0,
                        'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                    ];
                    if (request('task_type') == 'lead') {
                        if ($request->campaign_id) {
                            $call_exist = Task::query()->where([
                                'enquiry_id' => $request->enquiry_id,
                                'task_category_id' => 2,
                                'status' => 0,
                                'vendor_id' => $this->vendorId,
                            ])->whereNull('campaign_id')->orderBy('scheduled_date', 'DESC')->first();
                            if ($call_exist) {
                                $c_task = Task::find($call_exist->id);
                                $c_task->scheduled_date = $request->schedule_date_time;
                                $c_task->save();
                            } else
                                Task::create($input);
                        } else {
                            Task::create($input);
                        }
                    } else {
                        $input = [
                            'name' => 'Call Back ' . $enquiry->vchr_customer_name,
                            'description' => $request->comment,
                            'scheduled_date' => $request->schedule_date_time ?? now(),
                            'task_category_id' => 2,
                            'assigned_to' => $this->userId,
                            'assigned_by' => $this->userId,
                            'vendor_id' => $this->vendorId,
                            'deal_id' => $request->deal_id,
                            'comment' => $request->comment,
                            'status' => 0,
                            'call_status_id' => $request->call_status_id ?? 0,
                            'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                        ];
                        $tsk = DealTask::create($input);

                        $activity = new DealActivity();
                        $activity->note = 'Call Back ' . $enquiry->vchr_customer_name;
                        $activity->deal_id = $request->deal_id;
                        $activity->created_by = Auth::user()->pk_int_user_id;
                        $activity->log_type = DealActivity::TYPE_TASK;
                        $activity->date = $request->schedule_date_time ?? now();
                        $activity->task_id = $tsk->id;
                        $activity->task_category = 2;
                        $activity->task_status = 0;
                        $activity->save();
                    }
                }
                if ($request->enquiry_status_id == 15 && $request->agent_id) {
                    //Closed (15)-> Assign to New Agent not Mandatory
                    //Parameters:agent_id
                    $enquiry->staff_id = $request->agent_id;
                    $enquiry->assigned_date = Carbon::today();
                    $enquiry->save();
                    event(new LeadAssigned($enquiry->pk_int_enquiry_id, $this->userId));
                }

                if ($request->has('pool_id') && $request->pool_id) {
                    try {
                        $lead_camp = CampaignLead::query()->where('campaign_id', $request->campaign_id)->where('lead_id', $enquiry->pk_int_enquiry_id)->first();
                        $lead_camp->campaign_id = $request->pool_id;
                        $lead_camp->save();
                        $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($enquiry->staff_id);
                        $enquiry->staff_id = null;
                        $enquiry->save();
                        LeadCampaign::find($request->pool_id)->update(['is_complete' => 0]);
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                        \Log::info('lead cmapiagn call feedback datapool change issue');
                    }

                    try {
                        $note = ($staff) ? $staff->vchr_user_name . " has been unassigned from call feedback.Changed through data pool." : "Agent UnAssigned";
                        event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, $this->vendorId));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }

            } else {
                return $this->response(200, true, "Not Found", null);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::info("Error Occurred While Updating (done) Task through API:" . $exception->getMessage());
            return $this->response(200, true, $exception->getMessage(), null);
        }
        DB::commit();
        return $this->response(200, false, 'success', null);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * Deal Activity
     * <AUTHOR>
     */

    public function leadActivity()
    {
        Log::info('Lead activity from endpoint is being called with params', [
            'request' => request()->all()
        ]);

        $activities = EnquiryFollowup::query()
            ->with(['added_by:pk_int_user_id,vchr_user_name',
                'assigned:pk_int_user_id,vchr_user_name',
                'source:pk_int_enquiry_type_id,vchr_enquiry_type',
                'task:id,name',
                'task.campaign:id,name',
                'purpose:pk_int_purpose_id,vchr_purpose',
                'oldStatus:pk_int_feedback_status_id,vchr_status',
                'currentStatus:pk_int_feedback_status_id,vchr_status'])
            ->where('enquiry_id', request('id'))
            ->whereHas('enquiry', function ($query) {
                if ((request()->has('from') && request('from') != "") && (request()->has('to') && request('to') != "")) {
                    $query->whereDate('created_at', '>=', request('from'))
                        ->whereDate('created_at', '<=', request('to'));
                }
                if (request('enquiry_purpose_id')) {
                    $query->whereIn('enquiry_purpose_id', json_decode(request('enquiry_purpose_id')));
                }
                if (request('enquiry_source_id')) {
                    $query->whereIn('enquiry_source_id', json_decode(request('enquiry_source_id')));
                }
                if (request('enquiry_status_id')) {
                    $query->whereIn('enquiry_status_id', json_decode(request('enquiry_status_id')));
                }
                if (request('lead_type_id')) {
                    $query->whereIn('lead_type_id', json_decode(request('lead_type_id')));
                }
                if (request('staff_id')) {
                    $query->whereIn('staff_id', json_decode(request('staff_id')));
                }
            })
            ->orderByRaw("id DESC, created_at DESC")
            ->get();

        $activities = $activities->map(function ($activity) {
            if ($activity->log_type == 11) {
                if ($activity->ivr_voice_url != "")
                    return $this->getLeadActivityTrigger($activity);
            } else {
                return $this->getLeadActivityTrigger($activity);
            }
        })->filter();

        $enquiry = Enquiry::query()
            ->with(['visit:id,created_at,visit_note,date_in,agent_id,purpose_id',
                'visit.agent:pk_int_user_id,vchr_user_name',
                'visit.purpose:pk_int_purpose_id,vchr_purpose',
                'createdBy:pk_int_user_id,vchr_user_name',
                'assigned_user:pk_int_user_id,vchr_user_name'])
            ->find(request('id'));
        if (!$enquiry)
            $enquiry = Enquiry::onlyTrashed()->find(request('id'));

        $source = EnquiryType::select('vchr_enquiry_type')->find($enquiry->fk_int_enquiry_type_id ?? null);
        $newFollow = EnquiryFollowup::where('enquiry_id', request('id'))->where('log_type', EnquiryFollowup::TYPE_NEW)->first();

        if (!$newFollow) {
            $created_log = (object)[];
            $created_log->id = 0;
            $created_log->time = ($enquiry->created_at) ? Carbon::parse($enquiry->created_at)->diffForHumans() : '';
            $created_log->created_at = $enquiry->created_at->format('Y-m-d H:i');
            $created_log->created_by = $enquiry->createdBy ? $enquiry->createdBy->vchr_user_name : '';
            $created_log->agent_name = $enquiry->assigned_user ? $enquiry->assigned_user->vchr_user_name : '';
            $created_log->ivr_voice_url = null;
            $created_log->action = 'Created a';
            $created_log->activity = 'New Lead';
            $created_log->join_text = 'via';
            $created_log->content = ($source ? $source->vchr_enquiry_type : 'Website');
            $created_log->log_type = 0;
            $created_log->date = null;
            $created_log->task_id = null;
            $created_log->task_category = null;
            $created_log->task_status = null;
            $created_log->outcome = null;
            $activities->push($created_log);
        }
        $deal = Deal::with([
            'user:pk_int_user_id,vchr_user_name', 'agent:pk_int_user_id,vchr_user_name'
        ])->select('pk_int_deal_id', 'deal_name', 'created_at')->where('lead_id', request('id'))->get();
        $task = Task::with(['added_by:pk_int_user_id,vchr_user_name', 'agent:pk_int_user_id,vchr_user_name'])->select('id', 'name', 'campaign_id', 'scheduled_date', 'status', 'task_category_id', 'created_at')->where('enquiry_id', request('id'))->get();
        $taskDeal = DealTask::with(['added_by:pk_int_user_id,vchr_user_name', 'agent:pk_int_user_id,vchr_user_name'])->select('id', 'name', 'scheduled_date', 'task_category_id', 'status', 'created_at')->whereHas('deal', function ($q) {
            $q->where('lead_id', request('id'));
        })->get();

        $visits = $enquiry->visit;
        if ($deal)
            foreach ($deal as $value) {
                $obj['id'] = $value->pk_int_deal_id;
                $obj['time'] = $value->created_at->diffForHumans();
                $obj['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj['created_by'] = $value->user ? $value->user->vchr_user_name : '';
                $obj['agent_name'] = $value->agent ? $value->agent->vchr_user_name : '';
                $obj['ivr_voice_url'] = null;
                $obj['action'] = 'added a';
                $obj['activity'] = 'New Deal';
                $obj['join_text'] = '';
                $obj['content'] = $value->deal_name ?? "";
                $obj['old_content'] = '';
                $obj['log_type'] = EnquiryFollowup::TYPE_DEAL;
                $obj['date'] = $value->created_at->format('Y-m-d H:i');
                $obj['task_id'] = $value->pk_int_deal_id;
                $obj['task_category'] = null;
                $obj['task_status'] = null;
                $obj['outcome'] = null;
                $activities->push($obj);
            }
        if ($task)
            foreach ($task as $value) {
                $obj2['id'] = $value->id;
                $obj2['time'] = Carbon::parse($value->created_at)->diffForHumans();
                $obj2['created_at'] = Carbon::parse($value->created_at)->format('Y-m-d H:i');
                $obj2['created_by'] = $value->added_by ? $value->added_by->vchr_user_name : '';
                $obj2['agent_name'] = $value->agent ? $value->agent->vchr_user_name : '';
                $obj2['ivr_voice_url'] = null;
                $obj2['action'] = 'added a';
                $obj2['activity'] = $value->campaign_id ? 'New Campign Task' : 'New task';
                $obj2['join_text'] = '';
                $obj2['content'] = $value->name ?? '';
                $obj2['old_content'] = '';
                $obj2['log_type'] = EnquiryFollowup::TYPE_TASK_NEW;
                $obj2['date'] = Carbon::parse($value->scheduled_date)->format('Y-m-d H:i');
                $obj2['task_id'] = $value->id;
                $obj2['task_category'] = $value->task_category_id ?? null;
                $obj2['task_status'] = $value->status ?? null;
                $obj2['outcome'] = null;
                $obj2['task_type'] = 'lead';

                $activities->push($obj2);
            }
        if ($taskDeal) {
            $getDealName = 'New ' . getDealName(true) . ' task';
            foreach ($taskDeal as $value) {
                $obj3['id'] = $value->id;
                $obj3['time'] = $value->created_at->diffForHumans();
                $obj3['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj3['created_by'] = $value->added_by ? $value->added_by->vchr_user_name : '';
                $obj3['agent_name'] = $value->agent ? $value->agent->vchr_user_name : '';
                $obj3['ivr_voice_url'] = null;
                $obj3['action'] = 'added a';
                $obj3['activity'] = $getDealName;
                $obj3['join_text'] = '';
                $obj3['content'] = $value->name ?? '';
                $obj3['old_content'] = '';
                $obj3['log_type'] = EnquiryFollowup::TYPE_TASK_NEW;
                $obj3['date'] = Carbon::parse($value->scheduled_date)->format('Y-m-d H:i');
                $obj3['task_id'] = $value->id;
                $obj3['task_category'] = $value->task_category_id ?? null;
                $obj3['task_status'] = $value->status ?? null;
                $obj3['outcome'] = null;
                $obj3['task_type'] = 'deal';

                $activities->push($obj3);
            }
        }
        if ($visits)
            foreach ($visits as $value) {
                $obj2['id'] = $value->id;
                $obj2['time'] = $value->created_at->diffForHumans();
                $obj2['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj2['created_by'] = '';
                $obj2['agent_name'] = $value->agent->first() ? $value->agent->first()->vchr_user_name : '';
                $obj2['ivr_voice_url'] = null;
                $obj2['action'] = '';
                $obj2['activity'] = 'Customer visited';
                $obj2['join_text'] = '';
                $obj2['content'] = $value->visit_note ?? '';
                $obj2['old_content'] = '';
                $obj2['log_type'] = EnquiryFollowup::TYPE_VISIT;
                $obj2['date'] = Carbon::parse($value->date_in)->format('Y-m-d H:i');
                $obj2['task_id'] = $value->id;
                $obj2['task_category'] = null;
                $obj2['task_status'] = null;
                $obj2['outcome'] = null;
                $obj2['purpose'] = $value->purpose->first() ? $value->purpose->first()->vchr_purpose : '';

                $activities->push($obj2);
            }
        $activities = $activities->sortByDesc('created_at')->values();

        return $this->response(200, false, 'success', $activities);
    }

    public function homeTasks(): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = auth()->user();
        $vendorId = $user->getBusinessId();

        // Task query
        $tasksQuery = Task::query()
            ->nonCampaign()
            ->where('tasks.vendor_id', $vendorId)
            ->where('status', 0)
            ->leftJoin('task_categories', 'tasks.task_category_id', '=', 'task_categories.id')
            ->where('assigned_to', $user->pk_int_user_id)
            ->select([
                'tasks.id',
                'tasks.name',
                'description',
                'task_category_id',
                'scheduled_date',
                'tasks.vendor_id',
                'campaign_id',
                'status',
                DB::raw("'lead' as task_mode"),
                'task_categories.name as task_category',
                'enquiry_id',
                DB::raw("null as deal_id")
            ])
            ->addSelect([
                DB::raw("CASE 
                WHEN scheduled_date < CURDATE() THEN 'overdue' 
                WHEN DATE(scheduled_date) = CURDATE() THEN 'due' 
                ELSE 'pending' END AS task_status")
            ]);

        // DealTask query
        $dealTasksQuery = DealTask::query()
            ->where('deal_tasks.vendor_id', $vendorId)
            ->where('status', 0)
            ->leftJoin('deals', 'deals.pk_int_deal_id', '=', 'deal_tasks.deal_id')
            ->leftJoin('task_categories', 'deal_tasks.task_category_id', '=', 'task_categories.id')
            ->where('assigned_to', $this->userId)
            ->select([
                'deal_tasks.id',
                'deal_tasks.name',
                'description',
                'task_category_id',
                'scheduled_date',
                'deal_tasks.vendor_id',
                'campaign_id',
                'status',
                DB::raw("'deal' as task_mode"),
                'task_categories.name as task_category',
                'deals.lead_id as enquiry_id',
                'deal_tasks.deal_id'
            ])
            ->addSelect([
                DB::raw("CASE WHEN scheduled_date < CURDATE() THEN 'overdue' WHEN DATE(scheduled_date) = CURDATE() THEN 'due' ELSE 'pending' END AS task_status")
            ]);

        // Perform union using query builder
        $combinedQuery = $tasksQuery->union($dealTasksQuery);

        $collection = $combinedQuery->get();
        $callTasks = $collection->where('task_category_id', 2);
        $tasks = $collection->where('task_category_id', '<>', 2);

        // Get the start of today (midnight)
        $startOfToday = Carbon::today()->toDateTimeString();
        // Get the end of today (23:59:59)
        $endOfToday = Carbon::today()->endOfDay()->toDateTimeString();

        $todayCallTask = $callTasks->where('scheduled_date', '>=', $startOfToday)
            ->where('scheduled_date', '<=', $endOfToday)->sortBy('scheduled_date');
        $overDueTask = $callTasks->where('scheduled_date', '<', $startOfToday)->sortBy('scheduled_date');
        $callTaskList = $todayCallTask->union($overDueTask)->values();
        $data['call']['due_today'] = $todayCallTask->count();
        $data['call']['overdue'] = $overDueTask->count();
        $data['call']['list'] = $callTaskList->take(6);

        $todayNormalTask = $tasks->where('scheduled_date', '>=', $startOfToday)
            ->where('scheduled_date', '<=', $endOfToday)->sortBy('scheduled_date');
        $overDueNormalTask = $tasks->where('scheduled_date', '<', $startOfToday)->sortBy('scheduled_date');
        $normalTaskList = $todayNormalTask->union($overDueNormalTask)->values();

        $data['task']['due_today'] = $todayNormalTask->count();
        $data['task']['overdue'] = $overDueNormalTask->count();
        $data['task']['list'] = $normalTaskList->take(6);

        return $this->response(200, false, null, $data);
    }
}
