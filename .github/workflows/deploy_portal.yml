name: Deploy to production

on:
  workflow_dispatch:
    inputs:
      env:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - 'main'
          - 'portal'
          - 'alukkas'
          - 'care-hands'

jobs:
  call-endpoint:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Portal
        run: |
          # Set the endpoints variable based on the selected environment
          if [[ "${{ github.event.inputs.env }}" == "main" ]]; then
            echo "Deploying to 'main' environment"
            IFS=',' read -r -a ENDPOINTS <<< "${{ secrets.FORGE_MAIN_DEPLOY_API_ENDPOINTS }}"
          elif [[ "${{ github.event.inputs.env }}" == "portal" ]]; then
            echo "Deploying to 'portal' environment"
            IFS=',' read -r -a ENDPOINTS <<< "${{ secrets.FORGE_PORTAL_DEPLOY_API_ENDPOINTS }}"
          elif [[ "${{ github.event.inputs.env }}" == "alukkas" ]]; then
            echo "Deploying to 'portal' environment"
            IFS=',' read -r -a ENDPOINTS <<< "${{ secrets.FORGE_ALUKKAS_DEPLOY_API_ENDPOINTS }}"
          elif [[ "${{ github.event.inputs.env }}" == "care-hands" ]]; then
            echo "Deploying to 'portal' environment"
            IFS=',' read -r -a ENDPOINTS <<< "${{ secrets.FORGE_CAREHANDS_DEPLOY_API_ENDPOINTS }}"  
          else
            echo "Invalid environment selection"
            exit 1
          fi

          # Loop through each endpoint and call it
          for endpoint in "${ENDPOINTS[@]}"; do
            echo "Calling $endpoint"
            curl --fail -X GET "$endpoint"
            echo "Finished calling $endpoint"
          done
