<?php

declare(strict_types=1);

namespace Tests\Unit\Ivr\IVRWebhook\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\Jobs\AssignAgent;
use App\Ivr\Models\Ivr;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;
use Tests\LogEntry;
use Tests\LogFake;
use Tests\TestCase;

class AssignAgentTest extends TestCase
{
    use DatabaseTransactions;

    private AssignAgent $assignAgent;

    private int $vendorId;

    protected function setUp(): void
    {
        parent::setUp();

        $this->assignAgent = new AssignAgent();
        $this->vendorId = 1;

        Event::fake([CreateFollowup::class]);

        Log::swap(new LogFake());
    }

    /**
     * @test
     */
    public function it_skips_assignment_when_agent_already_assigned_to_call(): void
    {
        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543210',
            'mobile_no' => '9876543210',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => '+919876543210', // Already has agent assigned
        ]);

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: new PhoneNumber('+919876543213'),
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was not updated
        $call->refresh();
        $this->assertEquals('+919876543210', $call->agent_number);

        // Verify that no followup was created
        Event::assertNotDispatched(CreateFollowup::class);

        // Verify that appropriate log was written
        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Agent already assigned to call, skipping agent assignment'
                && $log->context === [
                    'agent_number' => '+919876543210',
                ]
        );
    }

    /**
     * @test
     */
    public function it_skips_assignment_when_event_has_no_agent_number(): void
    {
        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543211',
            'mobile_no' => '9876543211',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: null, // No agent number
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that no followup was created
        Event::assertNotDispatched(CreateFollowup::class);

        // Verify that appropriate log was written
        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Webhook has no agent assigned, So skipping agent assignment'
                && $log->context === [
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                ]
        );
    }

    /**
     * @test
     */
    public function it_updates_call_with_agent_number_when_agent_is_phone_number(): void
    {
        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543212',
            'mobile_no' => '9876543212',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $agentPhoneNumber = new PhoneNumber('+919876543213');

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneNumber,
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was updated with agent number
        $call->refresh();
        $this->assertEquals($agentPhoneNumber->toPhoneNumber(), $call->agent_number);
    }

    /**
     * @test
     */
    public function it_updates_call_with_agent_number_when_agent_is_string(): void
    {
        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543213',
            'mobile_no' => '9876543213',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $agentPhoneString = '919876543213';

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneString, // String instead of PhoneNumber
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was updated with agent number as string
        $call->refresh();
        $this->assertEquals($agentPhoneString, $call->agent_number);

        // Verify that no followup was created (since agent is not PhoneNumber)
        Event::assertNotDispatched(CreateFollowup::class);

        // Verify that appropriate log was written
        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Agent number is not a phone number, skipping agent assignment'
        );
    }

    /**
     * @test
     */
    public function it_skips_assignment_when_agent_not_found_in_database(): void
    {
        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543214',
            'mobile_no' => '9876543214',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $agentPhoneNumber = new PhoneNumber('+919876543213');

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneNumber,
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was updated with agent number
        $call->refresh();
        $this->assertEquals($agentPhoneNumber->toPhoneNumber(), $call->agent_number);

        // Verify that no followup was created (since agent not found)
        Event::assertNotDispatched(CreateFollowup::class);

        // Verify that appropriate log was written
        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Unable to assign agent to call enquiry as agent not found'
                && $log->context === [
                    'agent_number' => $agentPhoneNumber->toPhoneNumber(),
                ]
        );
    }

    /**
     * @test
     */
    public function it_skips_assignment_when_enquiry_already_has_staff(): void
    {
        $existingStaffId = 999;

        // Create agent user
        $agent = User::factory()->create([
            'mobile' => '9876543213',
            'parent_user_id' => $this->vendorId,
            'int_role_id' => User::STAFF,
            'vchr_user_name' => 'Test Agent',
        ]);

        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543215',
            'mobile_no' => '9876543215',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
            'staff_id' => $existingStaffId, // Already has staff assigned
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $agentPhoneNumber = new PhoneNumber('+919876543213');

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneNumber,
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was updated with agent number
        $call->refresh();
        $this->assertEquals($agentPhoneNumber->toPhoneNumber(), $call->agent_number);

        // Verify that enquiry staff was not changed
        $enquiry->refresh();
        $this->assertEquals($existingStaffId, $enquiry->staff_id);

        // Verify that no followup was created (since enquiry already has staff)
        Event::assertNotDispatched(CreateFollowup::class);
    }

    /**
     * @test
     */
    public function it_successfully_assigns_agent_to_enquiry(): void
    {
        $agentName = 'John Doe Agent';

        // Create agent user
        $agent = User::factory()->create([
            'mobile' => '9876543213',
            'parent_user_id' => $this->vendorId,
            'int_role_id' => User::STAFF,
            'vchr_user_name' => $agentName,
        ]);

        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543216',
            'mobile_no' => '9876543216',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
            'staff_id' => null, // No staff assigned
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        $agentPhoneNumber = new PhoneNumber('+919876543213');

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneNumber,
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that the call was updated with agent number
        $call->refresh();
        $this->assertEquals($agentPhoneNumber->toPhoneNumber(), $call->agent_number);

        // Verify that enquiry was assigned to agent
        $enquiry->refresh();
        $this->assertEquals($agent->pk_int_user_id, $enquiry->staff_id);
        $this->assertEquals(Carbon::today()->toDateString(), $enquiry->assigned_date);

        // Verify that followup was created
        Event::assertDispatched(
            CreateFollowup::class,
            fn ($event) => $event->note === $agentName . ' has been designated as the lead via IVR'
                && $event->log_type === EnquiryFollowup::TYPE_ACTIVITY
                && $event->enquiry_id === $enquiry->pk_int_enquiry_id
                && $event->created_by === $this->vendorId
        );

        // TODO: Add log assertions once we verify the basic functionality works
        // For now, let's just verify the core functionality without log checks
    }

    /**
     * @test
     */
    public function it_handles_agent_lookup_by_national_number(): void
    {
        $agentName = 'Jane Doe Agent';

        // Create agent user with national number (without country code)
        $agent = User::factory()->create([
            'mobile' => '9876543214', // National number only
            'parent_user_id' => $this->vendorId,
            'int_role_id' => User::STAFF,
            'vchr_user_name' => $agentName,
        ]);

        $enquiry = Enquiry::query()->create([
            'fk_int_user_id' => $this->vendorId,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '+919876543217',
            'mobile_no' => '9876543217',
            'country_code' => '+91',
            'fk_int_enquiry_type_id' => 1,
            'staff_id' => null,
        ]);

        $call = Ivr::factory()->create([
            'vendor_id' => $this->vendorId,
            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
            'agent_number' => null,
        ]);

        // Agent phone number with country code
        $agentPhoneNumber = new PhoneNumber('+919876543214');

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543211'),
            calledNumber: new PhoneNumber('+919876543212'),
            agentPhoneNumber: $agentPhoneNumber,
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWER',
            startTime: '2025-01-01 10:00:00',
            endTime: '2025-01-01 10:05:00',
            conversationDuration: 300,
            duration: 300,
            recordingUrl: 'https://example.com/recording.wav'
        );

        $this->assignAgent->forCall($call, $eventPayload);

        // Verify that agent was found and assigned using national number lookup
        $enquiry->refresh();
        $this->assertEquals($agent->pk_int_user_id, $enquiry->staff_id);

        // Verify that followup was created with correct agent name
        Event::assertDispatched(CreateFollowup::class, static fn ($event) => str_contains($event->note, $agentName));
    }
}
