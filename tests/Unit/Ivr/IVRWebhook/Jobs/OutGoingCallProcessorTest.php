<?php

declare(strict_types=1);

namespace Tests\Unit\Ivr\IVRWebhook\Jobs;

use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\CallMaster;
use App\Ivr\Enums\Direction;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\Jobs\IvrProviderEventPayloadParserFactory;
use App\Ivr\IVRWebhook\Jobs\OutGoingCallProcessor;
use App\Ivr\IVRWebhook\ManageEnquiry;
use App\Ivr\IVRWebhook\ParsesProviderEventPayload;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\IvrEventPush;
use Carbon\CarbonImmutable;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Shared\ValueObjects\PhoneNumber;
use Tests\LogEntry;
use Tests\LogFake;
use Tests\TestCase;

final class OutGoingCallProcessorTest extends TestCase
{
    use DatabaseTransactions;

    private OutGoingCallProcessor $processor;

    private IvrProviderEventPayloadParserFactory&MockInterface $parserFactory;

    private ParsesProviderEventPayload&MockInterface $parser;

    private ManageEnquiry&MockInterface $manageEnquiry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->parser = Mockery::mock(ParsesProviderEventPayload::class);
        $this->parserFactory = Mockery::mock(IvrProviderEventPayloadParserFactory::class);
        $this->manageEnquiry = Mockery::mock(ManageEnquiry::class);
        
        $this->parserFactory
            ->shouldReceive('getParser')
            ->andReturn($this->parser);

        $this->processor = new OutGoingCallProcessor(
            parserFactory: $this->parserFactory,
            manageEnquiry: $this->manageEnquiry
        );

        Log::swap(new LogFake());
    }

    /**
     * @test
     */
    public function it_returns_correct_state(): void
    {
        $this->assertEquals(IvrEvent::Outgoing, $this->processor->state());
    }

    /**
     * @test
     */
    public function it_skips_processing_if_call_record_exists(): void
    {
        $callUuid = $this->faker->uuid();

        Ivr::factory()
            ->outbound()
            ->create([
                'call_uuid' => $callUuid,
            ]);

        $eventPush = IvrEventPush::factory()
            ->outgoing()
            ->create([
                'external_id' => $callUuid,
                'vendor_id' => 1,
            ]);

        $this->parser->shouldNotHaveReceived('parse');

        $this->processor->process($eventPush);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'For outgoing call,Existing call record found, skipping'
                && $log->context === [
                    'call_uuid' => $callUuid,
                ]
        );

        $this->assertDatabaseCount(Ivr::class, 1);
    }

    /**
     * @test
     */
    public function it_processes_new_outgoing_call_event(): void
    {
        $callId = $this->faker->uuid();
        $vendorId = $this->faker->numberBetween(1, 100);
        $enquiryId = $this->faker->numberBetween(1, 100);
    $provider = Provider::VoxBayX;
        $callStartTime = '2025-03-28 14:54:20';
        $callEndTime = '2025-03-28 14:54:36';
        $conversationDuration = 14;

        $testData = [
            'CalledStatus' => 'ANSWER',
            'CalledDate' => '2025-03-28 14:54:35',
            'TotalCallDuration' => 16,
            'Pickduration' => '2025-03-28 14:54:35',
            'phoneNumber' => '************',
            'calledNumber' => '************',
            'agentPhoneNumber' => '************',
            'RecordingUrl' => 'https://x.voxbay.com:81/callcenter/get_outgoing_callrecordings_without_token/out_90727_************_28032025-145442.wav/2025-03-28/3274',
        ];

        $eventPush = IvrEventPush::factory()
            ->outgoing()
            ->forVendor($vendorId)
            ->create([
                'external_id' => $callId,
                'payload' => $testData,
                'provider' => $provider,
            ]);

        $payload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+' . $testData['phoneNumber']),
            calledNumber: new PhoneNumber('+' . $testData['calledNumber']),
            agentPhoneNumber: new PhoneNumber('+' . $testData['agentPhoneNumber']),
            dateTime: CarbonImmutable::parse($testData['CalledDate']),
            callStatus: $testData['CalledStatus'],
            startTime: $callStartTime,
            endTime: $callEndTime,
            conversationDuration: $conversationDuration,
            duration: $testData['TotalCallDuration'],
            recordingUrl: $testData['RecordingUrl']
        );

        $this->parser
            ->shouldReceive('parse')
            ->once()
            ->with($eventPush->payload, IvrEvent::Outgoing)
            ->andReturn($payload);

        $this->manageEnquiry
            ->shouldReceive('with')
            ->once()
            ->withArgs(
                static fn (string $type, PhoneNumber $mobileNumber, int $vendorIdArgs) => $type === EnquiryType::IVR
                    && $mobileNumber->equalsTo(new PhoneNumber('+' . $testData['phoneNumber']))
                    && $vendorIdArgs === $vendorId
            )
            ->andReturn($enquiryId);

        $this->processor->process($eventPush);

        $this->assertDatabaseHas(Ivr::class, [
            'vendor_id' => $vendorId,
            'call_uuid' => $callId,
            'caller_number' => $testData['phoneNumber'],
            'called_number' => $testData['calledNumber'],
            'fk_int_enquiry_id' => $enquiryId,
            'direction' => Direction::Outbound,
            'total_call_duration' => $testData['TotalCallDuration'],
            'call_status' => $testData['CalledStatus'],
            'recording_url' => $testData['RecordingUrl'],
            'agent_number' => $testData['agentPhoneNumber'],
            'call_start_time' => $callStartTime,
            'call_end_time' => $callEndTime,
            'conversation_duration' => $conversationDuration,
            'call_date' => $testData['CalledDate'],
        ]);

        $this->assertDatabaseHas(EnquiryFollowup::class, [
            'enquiry_id' => $enquiryId,
            'name' => 'Outgoing call',
            'note' => $callId,
            'log_type' => EnquiryFollowup::IVR,
            'response' => CallMaster::VOXBAY,
            'created_by' => $vendorId,
        ]);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Outgoing call event processed'
                && $log->context === [
                    'enquiry_id' => $enquiryId,
                    'call_uuid' => $callId,
                ]
        );
    }
}
